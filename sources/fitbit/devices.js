const axios = require("axios").default;

const { config } = require("../../environment/index");
const { log } = require("../../utils/logger");
const { getStaticSources } = require("../../utils/staticData");

async function getConnectedDevices(accessToken) {
  const url = config.fitbit.api_base_url + "/1/user/-/devices.json";
  const options = {
    method: "GET",
    url,
    headers: {
      "content-type": "application/json",
      authorization: "Bearer " + accessToken,
    }
  }
  try {
    const response = await axios(options);
    const sources = await getStaticSources();
    const devices = response.data?.map(device => {
      return {
        ...device,
        sourceId: sources.Fitbit.id
      }
    }) || [];
    return devices?.[0];
  } catch (error) {
    log.warn("Failed fetching Device details");
    log.warn(JSON.stringify(error));
    return [];
  }
}

const devices = {
  getConnectedDevices
};

module.exports = { devices };