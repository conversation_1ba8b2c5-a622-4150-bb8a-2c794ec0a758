const axios = require("axios").default;

const { config } = require("../../environment/index");
const { log } = require("../../utils/logger");

// Authentication
const apiUrl = config.fitbit.api_base_url + "/oauth2/token";
const basicAuth = Buffer.from(
  config.fitbit.client_id + ":" + config.fitbit.client_secret
).toString("base64");

async function getAccessToken(code, state) {
  const options = {
    method: "POST",
    data: `code=${code}&state=${state}&client_id=${config.fitbit.client_id}&grant_type=authorization_code`,
    url: apiUrl,
    headers: {
      "content-type": "application/x-www-form-urlencoded",
      authorization: "Basic " + basicAuth,
    },
  };
  const response = await axios(options);
  return response.data;
}

async function getRefreshToken(refreshToken) {
  const options = {
    method: "POST",
    data: `refresh_token=${refreshToken}&grant_type=refresh_token`,
    url: apiUrl,
    headers: {
      "content-type": "application/x-www-form-urlencoded",
      authorization: "Basic " + basicAuth,
    },
  };
  let response;
  try {
    response = await axios.post(apiUrl, `refresh_token=${refreshToken}&grant_type=refresh_token`,
    {headers: {
      "content-type": "application/x-www-form-urlencoded",
      authorization: "Basic " + basicAuth,
    }});
    return response.data;
  } catch (error) {
    log.warn(JSON.stringify(error));
    log.warn(JSON.stringify(error.response.data));
    throw error;
  }
}

async function revokeToken(token) {
  let response;
  const url = config.fitbit.api_base_url + "/oauth2/revoke";
  try {
    response = await axios.post(url, `token=${token}`,
      {headers: {
        "content-type": "application/x-www-form-urlencoded",
        authorization: "Basic " + basicAuth,
      }}
    );
    console.log(response)
    return response.data;
  } catch (error) {
    log.warn(JSON.stringify(error));
    log.warn(JSON.stringify(error.response.data));
  }
}

// Subscription
async function getSubscription(subscriptionId, accessToken) {
  try {
    const response = await axios.post(
      `https://api.fitbit.com/1/user/-/apiSubscriptions/${subscriptionId}.json`,
      {},
      {
        headers: {
          "content-type": "application/json",
          authorization: `Bearer ${accessToken}`,
          "X-Fitbit-Subscriber-Id": config.fitbit.subscriberId,
        },
      }
    );
    return response.data;
  } catch (error) {
    log.warn("Error occurred while subscribing for user with subscriptionId", subscriptionId);
    log.warn(JSON.stringify(error));
  }
}

async function getSubscriptionList(accessToken) {
  try {
    const response = await axios.get(
      `https://api.fitbit.com/1/user/-/apiSubscriptions.json`,
      {
        headers: {
          "content-type": "application/json",
          authorization: `Bearer ${accessToken}`,
        },
      }
    );
    return response.data?.apiSubscriptions;
  } catch (error) {
    log.warn("Error occurred while fetching subscriptions for user");
    log.warn(JSON.stringify(error));
  }
}

async function getUserProfile(accessToken) {
  const options = {
    method: "GET",
    url: "https://api.fitbit.com/1/user/-/profile.json",
    headers: {
      authorization: `Bearer ${accessToken}`,
    }
  };
  try {
    const response = await axios(options);
    return response.data.user;
  } catch (error) {
    log.warn("Error occurred while fetching user profile");
    log.warn(JSON.stringify(error));
    return null;
  }
}

const auth = {
  getAccessToken,
  getRefreshToken,
  getSubscription,
  getSubscriptionList,
  getUserProfile,
  revokeToken
};

module.exports = { auth };