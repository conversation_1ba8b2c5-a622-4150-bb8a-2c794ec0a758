const axios = require("axios").default;

const { config } = require("../../environment/index");
const { log } = require("../../utils/logger");

// Authentication
const apiUrl = config.dexcom.api_base_url + "/v2/oauth2/token";
const basicAuth = Buffer.from(
  config.dexcom.client_id + ":" + config.dexcom.client_secret
).toString("base64");

async function getAccessToken(code) {
  const formData = {
    grant_type: "authorization_code",
    code: code,
    redirect_uri: config.dexcom.authorize_redirect, 
    client_id: config.dexcom.client_id,
    client_secret: config.dexcom.client_secret,
  };

  const options = {
    method: "POST",
    url: apiUrl,
    headers: {
      "content-type": "application/x-www-form-urlencoded",
    },
    data: new URLSearchParams(formData).toString(),
  };

  try {
    const response = await axios(options);
    return response.data;
  } catch (error) {
    log.warn(JSON.stringify(error));
    log.info(JSON.stringify(error?.response?.data));
  }
}

async function getRefreshToken(refreshToken) {

  const formData = {
    grant_type: 'refresh_token',
    refresh_token: refreshToken,
    redirect_uri: config.dexcom.authorize_redirect, 
    client_id: config.dexcom.client_id,
    client_secret: config.dexcom.client_secret,
  }

  const options = {
    method: "POST",
    url: apiUrl,
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    },
    data: new URLSearchParams(formData).toString()
  };
  
  try {
    const response = await axios(options);
    return response.data;
  } catch (error) {
    log.warn(JSON.stringify(error));
    log.info(JSON.stringify(error?.response?.data));
  }
}

const auth = {
  getAccessToken,
  getRefreshToken,
};

module.exports = auth;
