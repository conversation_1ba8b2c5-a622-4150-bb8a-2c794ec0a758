const axios = require("axios").default;

const { config } = require("../../environment/index");
const { log } = require("../../utils/logger");
const { getStaticSources } = require("../../utils/staticData");

async function getConnectedDevices(accessToken) {
  let startDate = new Date()
  startDate.setDate(startDate.getDate() - 7)
  startDate = startDate.toISOString().split("T")[0]
  const endDate = new Date().toISOString().split("T")[0]

  const url = config.dexcom.api_base_url + "/v2/users/self/devices";
  const options = {
    method: "GET",
    url,
    headers: {
      "content-type": "application/json",
      authorization: "Bearer " + accessToken,
    },
    params: {
      startDate,
      endDate,
    }
  }
  console.log(startDate, endDate, accessToken, url, options)
  try {
    const response = await axios(options);
    const sources = await getStaticSources();
    const devices = response.data?.devices?.map(device => {
      const { alertScheduleList, ...rest } = device
      return {
        ...rest,
        sourceId: sources.Dexcom.id,
        sourceName: sources.Dexcom.name,
      }
    });
    return devices;
  } catch (error) {
    log.warn("Failed fetching Device details");
    log.warn(JSON.stringify(error));  
    return [];
  }
}

const devices = {
  getConnectedDevices
};

module.exports = devices;