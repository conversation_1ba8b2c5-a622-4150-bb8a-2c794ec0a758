const axios = require("axios").default;

const { config } = require("../../environment/index");
const { log } = require("../../utils/logger");
const { mapper } = require("./mappers");

async function getSleepData(userId, accessToken, startDate) {
  var url = config.oura.api_base_url + `/v2/usercollection/sleep`;
  const options = {
    method: "GET",
    url,
    headers: {
      "content-type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
    params: {
      start_date: startDate,
    },
  };
  try {
    const response = await axios(options);
    const sleepData = response?.data?.data || [];
    log.info(`Raw sleepData from Oura API: ${JSON.stringify(sleepData)}`);
    
    const mappedSleepLogs = await Promise.all(sleepData.map(async (log) => {
      return await mapper.mapSleepLogs(userId, log);
    }));
    
    const mappedHRVLogs = await Promise.all(sleepData.map(async (log) => {
      return await mapper.mapHRV(userId, log);
    }));
    
    const mappedRestingHeartRate = await Promise.all(sleepData.map(async (log) => {
      return await mapper.mapRestingHeartRate(userId, log);
    }));
    
    const mappedResponse = {
      sleepLogs: mappedSleepLogs.filter(obj => Object.keys(obj).length !== 0),
      hrvLogs: mappedHRVLogs.filter(obj => Object.keys(obj).length !== 0),
      restingHeartRate: mappedRestingHeartRate.filter(obj => Object.keys(obj).length !== 0)
    };
    
    return mappedResponse;
  } catch (error) {
    log.warn(JSON.stringify(error));
    log.warn(JSON.stringify(error?.response?.data));
    return [];
  }
}

async function getHeartRateByDate(userId, accessToken, startDate, endDate, offsetFromUTCMillis) {
  var url = config.oura.api_base_url + `/v2/usercollection/heartrate`;
  const options = {
    method: "GET",
    url,
    headers: {
      "content-type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
    params: {
      start_datetime: startDate,
      end_datetime: endDate,
    },
  };
  try {
    const response = await axios(options);
    const heartRateData = response?.data?.data || [];
    const mappedResponse = [await mapper.mapHeartRate(userId, heartRateData, offsetFromUTCMillis)];
    return mappedResponse;
  } catch (error) {
    log.warn(JSON.stringify(error));
    log.warn(JSON.stringify(error?.response?.data));
    return [];
  }
}

async function getActivitySummary(userId, accessToken, startDate) {
  var url = config.oura.api_base_url + `/v2/usercollection/daily_activity`;
  const options = {
    method: "GET",
    url,
    headers: {
      "content-type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
    params: {
      start_date: startDate,
    },
  };
  try {
    const response = await axios(options);
    const activitySummaryData = response?.data?.data || [];
    
    const mappedActivitySummary = await Promise.all(activitySummaryData.map(async (log) => {
      return await mapper.mapActivitySummary(userId, log);
    }));
    
    const mappedResponse = {
      activitySummary: mappedActivitySummary.filter(obj => Object.keys(obj).length !== 0)
    };
    
    return mappedResponse;    
  } catch (error) {
    log.warn(JSON.stringify(error));
    log.warn(JSON.stringify(error?.response?.data));
    return [];
  }
}

async function getActivityList(userId, accessToken, startDate) {
  var url = config.oura.api_base_url + `/v2/usercollection/workout`;
  const options = {
    method: "GET",
    url,
    headers: {
      "content-type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
    params: {
      start_date: startDate,
    },
  };
  try {
    const response = await axios(options);
    const activityListData = response?.data?.data || [];
    
    const mappedActivityList = await Promise.all(activityListData.map(async (log) => {
      return await mapper.mapActivityList(userId, log);
    }));
    
    const mappedResponse = {
      activityList: mappedActivityList.filter(obj => Object.keys(obj).length !== 0)
    };
    
    return mappedResponse;    
  } catch (error) {
    log.warn(JSON.stringify(error));
    log.warn(JSON.stringify(error?.response?.data));
    return [];
  }
}

async function getSpo2ByDate(userId, accessToken, startDate, endDate, offsetFromUTCMillis) {
  var url = config.oura.api_base_url + `/v2/usercollection/daily_spo2`;
  const options = {
    method: "GET",
    url,
    headers: {
      "content-type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
    params: {
      start_date: startDate,
      end_date: endDate,
    },
  };
  try {
    const response = await axios(options);
    const spo2Data = response?.data?.data || [];

    const mappedSpo2Logs = await Promise.all(spo2Data.map(async (log) => {
      return await mapper.mapSpo2ByDate(userId, log, offsetFromUTCMillis);
    }));
    
    const mappedResponse = mappedSpo2Logs.filter(obj => Object.keys(obj).length !== 0)
    return mappedResponse;    
  } catch (error) {
    log.warn(JSON.stringify(error));
    log.warn(JSON.stringify(error?.response?.data));
    return [];
  }
}

async function getTimeBasedTrackersData(userId, accessToken, lastSynced) {
  const afterTime = lastSynced.toISOString().split(".")[0]
  const startDate = lastSynced.toISOString().split('T')[0]
  log.info(`getTimeBasedTrackersData() | startDate: ${startDate} | afterTime: ${afterTime}`);
  const { sleepLogs, hrvLogs, restingHeartRate } = await getSleepData(userId, accessToken, startDate);
  const { activitySummary } = await getActivitySummary(userId, accessToken, startDate);
  const { activityList } = await getActivityList(userId, accessToken, startDate);
  const TimeBasedTrackersData = { 
    activitySummary,
    activityList,
    sleepLogs, hrvLogs, restingHeartRate
  };
  return TimeBasedTrackersData;
}

async function getDayBasedTrackersData(userId, accessToken, startTime, endTime, offsetFromUTCMillis) {
  const heartRateLogs = await getHeartRateByDate(userId, accessToken, startTime, endTime, offsetFromUTCMillis);
  const spo2Logs = await getSpo2ByDate(userId, accessToken, startTime.split('T')[0], endTime.split('T')[0], offsetFromUTCMillis);
  const dayBasedTrackersData = {
    heartRateLogs, spo2Logs
  };
  return dayBasedTrackersData;
}

const tracker = {
  getTimeBasedTrackersData,
  getDayBasedTrackersData,
};

module.exports = { tracker };
