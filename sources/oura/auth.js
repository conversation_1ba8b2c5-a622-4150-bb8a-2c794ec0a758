const axios = require("axios").default;

const { config } = require("../../environment/index");
const { log } = require("../../utils/logger");

// Authentication
const apiUrl = config.oura.api_base_url + "/oauth/token";
const basicAuth = Buffer.from(
  config.oura.client_id + ":" + config.oura.client_secret
).toString("base64");

async function getAccessToken(code) {
  const options = {
    method: "POST",
    data: `code=${code}&client_id=${config.oura.client_id}&client_secret=${config.oura.client_secret}&redirect_uri=${config.oura.authorize_redirect}&grant_type=authorization_code`,
    url: apiUrl,
    headers: {
      "content-type": "application/x-www-form-urlencoded",
      authorization: "Basic " + basicAuth,
    },
  };
  const response = await axios(options);
  return response.data;
}

async function getRefreshToken(refreshToken) {
  const options = {
    method: "POST",
    data: `refresh_token=${refreshToken}&grant_type=refresh_token`,
    url: apiUrl,
    headers: {
      "content-type": "application/x-www-form-urlencoded",
      authorization: "Basic " + basicAuth,
    },
  };
  let response;
  try {
    response = await axios.post(apiUrl, `refresh_token=${refreshToken}&grant_type=refresh_token`,
    {headers: {
      "content-type": "application/x-www-form-urlencoded",
      authorization: "Basic " + basicAuth,
    }});
    return response.data;
  } catch (error) {
    log.warn(JSON.stringify(error));
    log.warn(JSON.stringify(error?.response?.data));
    throw error;
  }
}

async function revokeToken(accessToken) {
  let response;
  const url = config.oura.api_base_url + "/oauth/revoke";
  try {
    response = await axios.post(url, `access_token=${accessToken}`,
      {headers: {
        "content-type": "application/x-www-form-urlencoded",
        authorization: "Basic " + basicAuth,
      }}
    );
    console.log(response)
    return response.data;
  } catch (error) {
    log.warn(JSON.stringify(error));
    log.warn(JSON.stringify(error?.response?.data));
  }
}

const auth = {
  getAccessToken,
  getRefreshToken,
  revokeToken
};

module.exports = { auth };
