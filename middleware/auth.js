const jwt = require('jsonwebtoken');
const { log } = require("../utils/logger");

module.exports.jwtValidator = function (req, res, next) {
    var accessTokenKey = "X-Access-Token".toLowerCase();
    var apiHeaderKey = "X-Api-Key".toLowerCase();
    var token = req.headers[accessTokenKey];
    var apiKey = req.headers[apiHeaderKey];
    if (apiKey) {
        next();
    } else if (token) {
        const decoded = jwt.decode(token, { complete: true });
        req.decoded = decoded.payload;
        next();
    } else {
        log.error('No token provided');
        return res.status(403).send({
            success: false, message: 'No token provided.'
        });
    }
};