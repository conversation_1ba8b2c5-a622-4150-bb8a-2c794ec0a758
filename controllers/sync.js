const { log } = require("../utils/logger");
const { sendSQSMessage } = require("../utils/aws");
const { userService } = require("../service/users");
const { getStaticSources } = require("../utils/staticData");

module.exports.hourlySync = async function (req, res, next) {
  const sources = await getStaticSources();
  log.info(`hourlySync() started: ${new Date().toISOString()}`);
  const fitbitUsers = await userService.getAllUsers(sources.Fitbit.id);
  const currentHourFitbitUsers = getCurrentHourUsers(fitbitUsers);
 
  const dexcomUsers = await userService.getAllUsers(sources.Dexcom.id);
  const currentHourDexcomUsers = getCurrentHourUsers(dexcomUsers);

  const ouraUsers = await userService.getAllUsers(sources.Oura.id);
  const currentHourOuraUsers = getCurrentHourUsers(ouraUsers);

  log.info(`currentHourFitbitUsers: ${JSON.stringify(currentHourFitbitUsers)}`);
  log.info(`currentHourDexcomUsers: ${JSON.stringify(currentHourDexcomUsers)}`);
  log.info(`currentHourOuraUsers: ${JSON.stringify(currentHourOuraUsers)}`);

  log.info(`Calling pushUsersInSQS()`);
  const { successfulUsers: successfulFitbitUsers, failedUsers: failedFitbitUsers } = await pushUsersInSQS(currentHourFitbitUsers, sources.Fitbit.name);
  const { successfulUsers: successfulDexcomUsers, failedUsers: failedDexcomUsers } = await pushUsersInSQS(currentHourDexcomUsers, sources.Dexcom.name);
  const { successfulUsers: successfulOuraUsers, failedUsers: failedOuraUsers } = await pushUsersInSQS(currentHourOuraUsers, sources.Oura.name);
  log.info(`successfulFitbitUsers: ${JSON.stringify(successfulFitbitUsers)}`);
  log.warn(`failedFitbitUsers: ${JSON.stringify(failedFitbitUsers)}`);
  log.info(`successfulDexcomUsers: ${JSON.stringify(successfulDexcomUsers)}`);
  log.warn(`failedDexcomUsers: ${JSON.stringify(failedDexcomUsers)}`);
  log.info(`successfulOuraUsers: ${JSON.stringify(successfulOuraUsers)}`);
  log.warn(`failedOuraUsers: ${JSON.stringify(failedOuraUsers)}`);
  return res.status(200).json({
    success: true,
    message: "Data synced",
    users: {
      fitbitUsers: currentHourFitbitUsers.map(user => user.userId),
      dexcomUsers: currentHourDexcomUsers.map(user => user.userId),
      ouraUsers: currentHourOuraUsers.map(user => user.userId)
    }
  });
};

function getCurrentHourUsers(users) {
  const usersInCurrentHours = [];
  const currentHour = new Date().getHours();
  for (let user of users) {
    const userCreatedAtHour = new Date(user.createdAt).getHours();
    if (userCreatedAtHour === currentHour && user.lastSynced) 
      usersInCurrentHours.push({
        userId: user.userId,
      });
  }
  return usersInCurrentHours;
}

async function pushUsersInSQS(userList, sourceName) {
  const successfulUsers = [];
  const failedUsers = [];
  for (const user of userList) {
    // sourceName will be used to construct sync endpoint
    const success = await sendSQSMessage(JSON.stringify({ userId: user.userId, sourceName }));
    if (success) {
      successfulUsers.push(user.userId);
    } else {
      failedUsers.push(user.userId);
    }
  }

  return { successfulUsers, failedUsers };
}
