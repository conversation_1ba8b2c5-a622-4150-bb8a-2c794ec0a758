const { async<PERSON><PERSON><PERSON>, deepCopy } = require("../utils/helpers");
const { log: logger } = require("../utils/logger");
const { devicesService } = require("../service/devices")
const { userService } = require("../service/users")
const { getFitbitAccessToken } = require("./fitbit");
const fitbit = require("../sources/fitbit");
const { revokeFitbitToken, getOAuthUrl: getFitbitOAuthUrl } = require("./fitbit");
const { revokeDexcomToken, getOAuthUrl: getDexcomOAuthUrl } = require("./dexcom");
const { revokeOuraToken, getOAuthUrl: getOuraOAuthUrl } = require("./oura");
const { getStaticSourceSummary, getStaticDevices, getStaticDevicesWithoutManualEntry, getStaticSources } = require("../utils/staticData");

const Ajv = require("ajv");
const addFormats = require("ajv-formats");

const ajv = new Ajv({ allErrors: true });
addFormats(ajv);
const validate = ajv.compile(require("../models/devices.json"));

module.exports.getDevicesById = async (req, res, next) => {
  let userAgent = req.headers?.['user-agent'];
  const { user_guid: userGuid } = req.decoded;
  if (!userGuid) {
    return next({ message: "user_id is missing in request", statusCode: 400 });
  }

  let deviceId = req.params.id;
  const invalidRegex = /\D/;
  if (!deviceId?.trim() || invalidRegex.test(deviceId)) {
    return next({ message: "invalid or missing deviceId in request", statusCode: 400 });
  }
  
  const sourceSummary = await getStaticSourceSummary(); 
  const devices = await getStaticDevicesWithoutManualEntry();
  let data = deepCopy(devices)
  data = data.filter(x => x.id == deviceId)[0] || {}

  const response = await devicesService.getConnectedDevices(userGuid);
  const connectedDevices = response?.connectedDevices || [];

  if (Object.keys(data).length && connectedDevices.length) {
    const matched = connectedDevices.find(cdv => cdv.id == data.id);
    if (matched) data = { 
      ...data, 
      "name": matched.name || data.name,
      trackers:  matched?.trackers || [],
      "isConnected": matched.isConnected || data.isConnected,
      "serialNumber": matched.serialNumber || data.serialNumber,
    }
  }

  if (Object.keys(data).length) {
    data["source"] = sourceSummary[data["sourceId"].toString()]
    delete data.sourceId

    if (userAgent == 'ios' || userAgent == 'android') {
      if (!data?.source?.platform.includes(userAgent)) {
        return res.status(404).json({
          success: false,
          message: `Device not supported by the user agent.`,
        });
      }
    }    

    // Fitbit devices
    const fitbitDevice = await fetchFitbitDeviceDetails(userGuid)
    if (fitbitDevice && Object.keys(fitbitDevice).length) {
      if (data?.source?.id == fitbitDevice.sourceId) {
        const {sourceId, ...others} = fitbitDevice
        data = {...data, ...others}
      }
    }

    // Oura devices
    const OuraDevice = await fetchOuraDeviceDetails(userGuid)
    if (OuraDevice && Object.keys(OuraDevice).length) {
      if (data?.source?.id == OuraDevice.sourceId) {
        const {sourceId, ...others} = OuraDevice
        data = {...data, ...others}
      }
    }

    return res.status(200).json({
      success: true,
      data,
    });
  }

  res.status(404).json({
    success: false,
    message: `No device found`,
  });
};

module.exports.getConnectedDevices = async (req, res, next) => {
  let userAgent = req.headers?.['user-agent'];
  const userId = req.query?.user_guid || req.decoded.user_guid;
  if (!userId) {
    return next({ message: "user_guid is missing in request", statusCode: 400 });
  }

  let sourceId = req.query?.sourceId;
  let trackers = req.query?.trackerIds ? req.query?.trackerIds.split(",") : [];  
  trackers = trackers.map(tr => parseInt(tr)).filter(x => x)

  const sources = await getStaticSources();
  const sourceSummary = await getStaticSourceSummary();
  const devices = await getStaticDevicesWithoutManualEntry();
  let data = deepCopy(devices);
  if (sourceId) {
    data = data.filter(
      device => device.sourceId == sourceId
    )
  }
  if (trackers.length) {
    data = data.filter(
      device => trackers.every(tr => 
        device.trackers.some(dvTr => dvTr.trackerId == tr)
      )
    )
  }
  const response = await devicesService.getConnectedDevices(userId);
  const connectedDevices = response?.connectedDevices || [];

  data = data.map(dv => {
    const matched = connectedDevices.find(cdv => cdv.id == dv.id);
    if (matched) return { 
      ...dv,
      "name": matched.name || dv.name,
      "trackers":  matched?.trackers || [],
      "isConnected": matched.isConnected || dv.isConnected,
      "serialNumber": matched.serialNumber || dv.serialNumber,      
    }
    return dv
  })
  .filter(x => x)
  .filter(x => {
    if (!req.query?.isConnected) return true;
    else if (req.query?.isConnected == 'true' && x?.isConnected) return true
    else if (req.query?.isConnected == 'false' && !x?.isConnected) return true
  }).map(dv => {
    const {sourceId, ...others} = dv;
    return {...others, source: sourceSummary[sourceId.toString()]}
  });

  if (userAgent == 'ios' || userAgent == 'android') {
    data = data.filter(
      dv => dv?.source?.platform.includes(userAgent)
    )
  }

  // Fitbit device
  const fitbitDevice = await fetchFitbitDeviceDetails(userId);
  if (fitbitDevice && Object.keys(fitbitDevice).length) {
    data = await Promise.all(
      data.map(async (dv) => {
        if (dv?.source.id === fitbitDevice.sourceId) {
          const { sourceId, ...others } = fitbitDevice;
          dv.source.detail.OAuthURL = await getFitbitOAuthUrl(userId, false);
          return { ...dv, ...others };
        }
        return dv;
      })
    );
  }
  
  // Oura device
  const OuraDevice = await fetchOuraDeviceDetails(userId);
  if (OuraDevice && Object.keys(OuraDevice).length) {
    data = await Promise.all(
      data.map(async (dv) => {
        if (dv?.source.id === OuraDevice.sourceId) {
          const { sourceId, ...others } = OuraDevice;
          dv.source.detail.OAuthURL = await getOuraOAuthUrl(userId, false);
          return { ...dv, ...others };
        }
        return dv;
      })
    );
  }  

  // // Dexcom device
  const DexcomDevice = await fetchDexcomDeviceDetails(userId);
  if (DexcomDevice && Object.keys(DexcomDevice).length) {
    data = await Promise.all(
      data.map(async (dv) => {
        if (dv?.source.id === DexcomDevice.sourceId) {
          const { sourceId, ...others } = DexcomDevice;
          dv.source.detail.OAuthURL = await getDexcomOAuthUrl(userId, false);
          return { ...dv, ...others };
        }
        return dv;
      })
    );
  }  

  // Google Fit & Apple Health
  // Here Logs are being posted via bulkPost/single post, not using OAuth
  const sourceIds = [sources.GoogleFit.id, sources.AppleHealth.id, sources.iHealth.id];
  const userDetailBySourceIds = await userService.getUserDetailsBySourceIds(userId, sourceIds);
  const deviceIdLastSyncMapping = userDetailBySourceIds.reduce((acc, userDetail) => {
    for (const deviceId in userDetail.devices) {
      acc[deviceId] = userDetail.devices[deviceId].lastSynced || null;
    }
    return acc;
  }, {});
  
  data = data.map((dv) => {
    if (deviceIdLastSyncMapping[dv.id]) {
      return { ...dv, lastSync: deviceIdLastSyncMapping[dv.id] };
    }
    return dv;
  });

  if (data.length) {
    return res.status(200).json({
      success: true,
      data,
    });
  }

  return res.status(200).json({
    success: true,
    message: `No connected devices found`,
    data: [],
  });
};

module.exports.connectSDKDevice = asyncHandler(async (req, res, next) => {
  const { user_guid: userId } = req.decoded;
  if (!userId) {
    return next({ message: "user_id is missing in request", statusCode: 400 });
  }

  const valid = validate({connectedDevices: [req.body]});
  if (!valid) { 
    return next({
      message: 'Validation Failed', error: validate.errors, statusCode: 400,
    });
  }
  
  // Handle OAuth disconnection
  const devices = await getStaticDevicesWithoutManualEntry();
  const OAuthDeviceIds = await getOAuthDeviceIds();
  if (OAuthDeviceIds.includes(req.body?.id)) {
    if (!req.body?.isConnected) {
      const device = devices.find(d => d.id == req.body?.id)
      if (!device) return next({
        message: "Device Id not matched", statusCode: 400,
      });

      switch (device.id) {
        case 11: {
          await revokeFitbitToken(userId); break;
        }
        case 21: {
          await revokeDexcomToken(userId); break;
        }
        case 61: {
          await revokeOuraToken(userId); break;
        }
        default: {
          return next({
            message: "Can't update. Device not found.", statusCode: 400,
          });
        }
      }     
    } else {
      return next({
        message: "Can't update OAuth device", statusCode: 400,
      });
    }
  }

  const now = new Date()
  const newDoc = {
     userId,
     newDevice: req.body,
     createdAt: now.toISOString(), 
     updatedAt: now.toISOString()
   }; 

  const accessToken = req.headers['x-access-token'];
  const response = await devicesService.connectNewDevices(userId, newDoc, true, accessToken);
  const connectedDevices = response?.connectedDevices || [];
  const device = connectedDevices.find(dev => dev.id === req.body.id);
  if (!device?.isConnected) {
    return res.status(200).json({
      success: true, message: "Token Revoked"
    }); 
  }
  return res.status(200).json({
    success: true,
    message: `Log entry saved`,
    data: { insertedId: response.id },
  });
});

async function fetchFitbitDeviceDetails(userId) {
  try {
    const sources = await getStaticSources();
    const fitbitAccessToken = await getFitbitAccessToken(userId);
    const userDetails = await userService.getUserDetails(userId, sources.Fitbit.id)
    const fitbitDevice = await fitbit.devices.getConnectedDevices(fitbitAccessToken)
    return {
      sourceId: fitbitDevice.sourceId,
      battery: fitbitDevice.batteryLevel,
      deviceName: fitbitDevice.deviceVersion,
      serialNumber: [fitbitDevice.id],
      lastSync: userDetails.lastSynced
    }
  } catch (e) { console.log(e) }
}

async function fetchOuraDeviceDetails(userId) {
  try {
    const sources = await getStaticSources();
    const userDetails = await userService.getUserDetails(userId, sources.Oura.id)
    return {
      sourceId: userDetails?.sourceId || null,
      lastSync: userDetails?.lastSynced || null
    }
  } catch (e) { console.log(e) }
}

async function fetchDexcomDeviceDetails(userId) {
  try {
    const sources = await getStaticSources();
    const userDetails = await userService.getUserDetails(userId, sources.Dexcom.id)
    return {
      sourceId: userDetails?.sourceId || null,
      lastSync: userDetails?.lastSynced || null
    }
  } catch (e) { console.log(e) }
}

async function getOAuthDeviceIds() {
  const devices = await getStaticDevices();
  const sources = await getStaticSources();
  const OAuthDeviceIds = devices.filter(d => {
    const sourceId = d.sourceId
    return Object.keys(sources).find(sKey =>
      sources[sKey].id == sourceId && sources[sKey]?.detail?.type == "OAuth"
    )
  }).map(d => d.id);
  return OAuthDeviceIds;
}

module.exports.getOAuthUrlByDeviceId = async function (userId, deviceId) {
  switch (Number(deviceId)) {
    case 11:
      return getFitbitOAuthUrl(userId, false);
    case 21:
      return getDexcomOAuthUrl(userId, false);
    case 61:
      return getOuraOAuthUrl(userId, false);
  }
  return null;
}
