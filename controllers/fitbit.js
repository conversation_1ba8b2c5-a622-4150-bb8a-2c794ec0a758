const { v4: uuidv4 } = require("uuid");

const { log } = require("../utils/logger");
const { getDateAfterSeconds, getDuration } = require("../utils/helpers");
const { config } = require("../environment/index");
const { sendSQSMessage } = require("../utils/aws");

const fitbit = require("../sources/fitbit");
const { userService } = require("../service/users");
const { devicesService } = require("../service/devices");
const { trackersService } = require("../service/trackers");

const { getStaticSources, getStaticSourceDevices } = require("../utils/staticData");
const { getUTCOffsetValue } = require("../service/userProfile");
const { addLogsToQueue } = require("../utils/trackersIngestion");

const trackersFitbitScopeMap = {
  nutrition: [2], activity: [3, 4], sleep: [5],
  cardio_fitness: [12], heartrate: [6, 10, 11], respiratory_rate: [],
  electrocardiogram: [13], oxygen_saturation: [9, 12],
  weight: [15, 16, 17], temperature: [18], mindfulness: [24],
  stress: [], location: [], profile: [], settings: []  
}

// Fitbit Metadata
async function getMetadata(req, res, next) {
  const userId = req.decoded.user_guid;
  if (!userId) {
    return res.status(400).send({ success: false, message: "userId is missing in request" });
  }
  
  const authorizeUrl = await getOAuthUrl(userId);
  return res.status(200).send({
    success: true,
    message: 'Visit Authorise URL to proceed with OAuth Login.',
    data: {
      authorizeUrl,
    },
  });
};

async function callback(req, res, next) {
  const code = req.query.code;
  const state = req.query.state;
  if (!code || !state) {
    return res.status(400).send({
      success: false,
      message: "Required query param code/state missing",
    });
  }
  const sources = await getStaticSources();
  const userDetails = await userService.getUserByState(state, sources.Fitbit.id);    
  log.info(`Fitbit callback userDetails`, JSON.stringify(userDetails));
  if (!userDetails) {
    return res.status(400).send({
      success: false,
      message: "Invalid state input provided",
    });
  }
  const fitbitResponse = await fitbit.auth.getAccessToken(code, state);
  log.info(`getAccessToken for userId: ${userDetails.userId}`, JSON.stringify(fitbitResponse));
  // Create or Get subscription

  const subscriptions = await fitbit.auth.getSubscriptionList(fitbitResponse?.access_token)
  const subscriptionId = subscriptions?.[0]?.subscriptionId || uuidv4();
  await storeFitbitDetails(userDetails.userId, fitbitResponse, subscriptionId);
  const accessToken = req.headers['x-access-token'];
  await updateDeviceScope(userDetails.userId, fitbitResponse, accessToken);
  if (!subscriptions || !subscriptions.length) {
    await fitbit.auth.getSubscription(
      subscriptionId,
      fitbitResponse?.access_token
    );
  }

  const userProfile = await fitbit.auth.getUserProfile(fitbitResponse?.access_token);

  if (userProfile) {
    const offsetFromUTCMillis = userProfile?.offsetFromUTCMillis || 0;
    await userService.upsertUserDetails(userDetails.userId, { offsetFromUTCMillis }, sources.Fitbit.id);

    // Syncing logs for duration = 7, once device is connected
    const fitbitLogs = await syncFitbitData(userDetails, 7);

    return res.status(200).send({
      success: true,
      message: "Success",
    });
  }  

  return res.status(400).send({
    success: false,
    message: "Invalid user details",
  });
};

// Subscriber Verification
async function getNotification(req, res, next) {
  const verificationCode = req.query.verify;
  if (verificationCode === config.fitbit.verificationCode) {
    return res.status(204).end();
  }
  return res.status(404).send();
};

// Notification
async function postNotification(req, res, next) {
  log.info("Fitbit notification received");
  log.info(JSON.stringify(req.body));

  // req.body is an array of subscription objects
  const subscriptionsData = req.body; 

  const sources = await getStaticSources();
  for (const subscription of subscriptionsData) {
    const subscriptionId = subscription?.subscriptionId;
    const userDetails = await userService.getUserBySubscriptionId(subscriptionId);
    const userId = userDetails?.userId;
    if (userId) {
      // sourceName will be used to construct sync endpoint
      await sendSQSMessage(JSON.stringify({ userId, sourceName: sources.Fitbit.name }));
    }
  }
  return res.status(204).send();
};

// Function F2
async function ManualSync(req, res, next) {
  const userId = req.query?.user_guid || req.decoded.user_guid;
  let duration = Number(req.query?.duration || -1);
  if (!userId)
    return res.status(400).send({ success: false, message: "user_id missing in request", });
  
  const sources = await getStaticSources();
  const userDetails = await userService.getUserDetails(userId, sources.Fitbit.id);
  if (!userDetails)
    return res.status(400).send({ success: false, message: "User is not connected with Fitbit", });

  /** 
   * Conditions to modify duration, incase not passed in query param
   * if lastSynced exists ->
   *    if timeDiff between current time & lastSynced is greater than 7 days, then duration = 7
   *    else duration = 0
   * if lastSynced doesn't exist ->
   *    then duration = 7
  */
  if (duration === -1) {
    if (!userDetails.lastSynced) {
      duration = 7;
    } else {
      const lastSyncedDate = new Date(userDetails.lastSynced);
      const timeDiff = getDuration(lastSyncedDate.toISOString(), new Date().toISOString());
      duration = Math.min(7, Math.ceil(timeDiff / (1000 * 60 * 60 * 24)));
    }
  }
  
  try {
    const fitbitLogs = await syncFitbitData(userDetails, duration);
    return res.status(200).json({
      success: true,
      data: fitbitLogs,
    });
  }
  catch (error) {
    log.error(`Fitbit | ManualSync error, ${JSON.stringify(error)}`);
    return next({ message: error?.message, statusCode: 401 });
  }
};

// Sync with Fitbit
async function syncFitbitData(userDetails, duration = 1) {
  try {
    const UTCOffsetMin = await getUTCOffsetValue(userDetails.userId);
    if(!userDetails?.offsetFromUTCMillis && userDetails?.offsetFromUTCMillis != 0)
      userDetails.offsetFromUTCMillis = UTCOffsetMin * 60 * 1000;

    log.info(`Syncing Fitbit data for userID: ${userDetails.userId}`)
    log.info(`lastSynced from DB: ${userDetails.lastSynced} | offsetFromUTCMillis: ${userDetails?.offsetFromUTCMillis} | duration: ${duration}`);
    let lastSynced;
    if (duration === 0) {
      lastSynced = new Date(userDetails.lastSynced);
      lastSynced.setMilliseconds(lastSynced.getMilliseconds() + userDetails?.offsetFromUTCMillis || 0);
    } else {
      lastSynced = new Date(Date.now() + userDetails?.offsetFromUTCMillis || 0);
      lastSynced.setDate(lastSynced.getDate() - duration);
    }
    log.info("offsetFromUTCMillis adjusted lastSynced: ", lastSynced.toISOString())
    const sources = await getStaticSources();

    // Fetch user's access token (after refresh)
    const accessToken = await getFitbitAccessToken(userDetails.userId);
    log.debug("Retreived Access token");

    if (!accessToken) {
      throw new Error("Access token not found. Please ensure that you have a valid access token.");
    }
    
    // Fetch all activity & sleep logs
    const timeBasedTrackerLogs = await fitbit.tracker.getTimeBasedTrackersData(userDetails.userId, accessToken, lastSynced, userDetails?.offsetFromUTCMillis || 0);
    log.info("Time based tracker logs");
    log.info(`timeBasedTrackerLogs: ${JSON.stringify(timeBasedTrackerLogs)}`);

    // Upsert day based trackers
    const currentDT = new Date();
    currentDT.setUTCHours(0,0,0,0)
    currentDT.setMilliseconds(currentDT.getMilliseconds() + userDetails?.offsetFromUTCMillis || 0);
    const lastSyncedDate = new Date(userDetails.lastSynced);
    lastSyncedDate.setUTCHours(0,0,0,0)
    lastSyncedDate.setMilliseconds(lastSyncedDate.getMilliseconds() + userDetails?.offsetFromUTCMillis || 0);
    if (duration) lastSyncedDate.setDate(lastSyncedDate.getDate() - duration);
    const allDayData = { activitySummary: [], weightLogs: [], bmiLogs:[], fatLogs: [],
      waterLogs: [], heartRateLogs: [], hrvLogs: [], spo2Logs: [],
      vo2Logs: [], restingHeartRateLogs: []
    };
    log.info(`currentDT: ${currentDT} | currentDT.toISOString(): ${currentDT.toISOString()}`)
    log.info("Last Sync Date", lastSyncedDate.toISOString())

    while (currentDT >= lastSyncedDate) {
      log.info(`Calling day based trackers for date: ${currentDT}`)
      const dayBasedTrackersData = await fitbit.tracker.getDayBasedTrackersData(userDetails.userId, accessToken, currentDT, userDetails?.offsetFromUTCMillis || 0);

      log.info("Inserting Water log")
      if (dayBasedTrackersData?.waterLogs && Object.keys(dayBasedTrackersData.waterLogs).length > 0) {
        const log = dayBasedTrackersData.waterLogs;
        allDayData.waterLogs.push(log); 
      }

      log.info("Inserting Activity summary log")
      if (dayBasedTrackersData?.activitySummary && Object.keys(dayBasedTrackersData.activitySummary).length > 0) {
        const log = dayBasedTrackersData.activitySummary;
        allDayData.activitySummary.push(log);
      }
      
      log.info("Inserting SpO2 log")
      if (dayBasedTrackersData?.spo2Logs && Object.keys(dayBasedTrackersData.spo2Logs).length > 0) {
        const log = dayBasedTrackersData.spo2Logs;
        allDayData.spo2Logs.push(log); 
      }
      
      log.info("Inserting Heart rate log")
      if (dayBasedTrackersData?.heartRateLogs && Object.keys(dayBasedTrackersData.heartRateLogs).length > 0) {
        const log = dayBasedTrackersData.heartRateLogs;
        allDayData.heartRateLogs.push(log); 
      }

      log.info("Inserting RHR log")
      if (dayBasedTrackersData?.restingHeartRate && Object.keys(dayBasedTrackersData.restingHeartRate).length > 0) {
        const log = dayBasedTrackersData.restingHeartRate;
        allDayData.restingHeartRateLogs.push(log); 
      }  

      log.info("Inserting HRV log")
      if (dayBasedTrackersData?.hrvLogs && Object.keys(dayBasedTrackersData.hrvLogs).length > 0) {
        const log = dayBasedTrackersData.hrvLogs;
        allDayData.hrvLogs.push(log); 
      }  
      
      log.info("Inserting Vo2 log")
      if (dayBasedTrackersData?.vo2Logs && Object.keys(dayBasedTrackersData.vo2Logs).length > 0) {
        const log = dayBasedTrackersData.vo2Logs;
        allDayData.vo2Logs.push(log); 
      } 

      log.info("Inserting Weight, Fat, BMI log")
      if (dayBasedTrackersData?.weightLogs && Object.keys(dayBasedTrackersData.weightLogs).length > 0) {
        const log = dayBasedTrackersData.weightLogs;
        allDayData.weightLogs.push(log);      
      }
      if (dayBasedTrackersData?.fatLogs && Object.keys(dayBasedTrackersData.fatLogs).length > 0) {
        const log = dayBasedTrackersData.fatLogs;
        allDayData.fatLogs.push(log); 
      }    
      if (dayBasedTrackersData?.bmiLogs && Object.keys(dayBasedTrackersData.bmiLogs).length > 0) {
        const log = dayBasedTrackersData.bmiLogs;
        allDayData.bmiLogs.push(log); 
      }    

      currentDT.setDate(currentDT.getDate() - 1);
    }
    await postProcessSync(userDetails.userId, timeBasedTrackerLogs, allDayData);  
    return { ...timeBasedTrackerLogs, ...allDayData };
  }
  catch (error) {
    log.error(`Error in syncFitbitData: ${JSON.stringify(error)}`);
  }
}

async function postProcessSync(userId, timeBasedTrackerLogs, allDayData) {
  const trackerIdLogsMapping = {
    4: timeBasedTrackerLogs?.activityList || [],
    5: timeBasedTrackerLogs?.sleepLogs || [],
    13: timeBasedTrackerLogs?.ecgLogs || [],
    18: timeBasedTrackerLogs?.tempLogs || [],
    3: allDayData?.activitySummary || [],
    15: allDayData?.weightLogs || [],
    17: allDayData?.bmiLogs || [],
    16: allDayData?.fatLogs || [],
    2: allDayData?.waterLogs || [],
    10: allDayData?.heartRateLogs || [],
    11: allDayData?.hrvLogs || [],
    9: allDayData?.spo2Logs || [],
    12: allDayData?.vo2Logs || [],
    25: allDayData?.restingHeartRateLogs || [],
  }; 
  log.info(`fitbit postProcessSync for userId: ${userId}, trackerIdLogsMapping: ${JSON.stringify(trackerIdLogsMapping)}`);
  const isPushedToSQS = await addLogsToQueue(userId, trackerIdLogsMapping);
  log.info(`fitbit postProcessSync for userId: ${userId}, isPushedToSQS: ${isPushedToSQS}`);
  return true;
}

// Revoke Token
async function revokeFitbitToken(userId, isRefreshTokenValid = true) {
  log.info(`Revoking fitbit token for userId: ${userId}`);
  const sources = await getStaticSources();
  const sourceDevices = await getStaticSourceDevices();
  if(isRefreshTokenValid) {
    const token = await getFitbitAccessToken(userId)

    // Revoke access token
    const deletedToken = await fitbit.auth.revokeToken(token)
    if (!deletedToken) { log.info("Invalid request, can't revoke token") }
  }

  // Remove user Fitbit details
  const deletedUserDoc = await userService.deleteUser(userId, sources.Fitbit.id);
  if (!deletedUserDoc) {log.info("Revoked token but Failed to delete user")}
  
  // Remove fitbit default device
  const resp = await trackersService.getAllDefaultTrackers(userId)
  if(resp && resp.defaultDevices) {
    resp.defaultDevices = resp.defaultDevices.filter(d => {
      d.deviceId != sourceDevices[sources.Fitbit.id]?.[0]?.id
    })
    await trackersService.upsertTrackers(userId, resp)
  }
  // Remove connected device
  const deviceIds = Object.keys(deletedUserDoc?.devices || {});
  await devicesService.deleteConnectedDevice(userId, deviceIds);

  log.info(`Revoked user token for Fitbit for userId: ${userId}`)
}

// Helpers
async function storeFitbitDetails(userId, fitbitResponse, subscriptionId) {
  const sources = await getStaticSources();
  // Update User data
  var data = {
    accessToken: fitbitResponse?.access_token,
    refreshToken: fitbitResponse?.refresh_token,
    sourceUserId: fitbitResponse?.user_id,
    subscriptionId,
  };
  data.expiresAt =
    data.accessToken && fitbitResponse.expires_in
      ? getDateAfterSeconds(fitbitResponse.expires_in - 60)
      : null;
  await userService.upsertUserDetails(userId, data, sources.Fitbit.id);
}

async function updateDeviceScope(userId, fitbitResponse, accessToken) {
  const sources = await getStaticSources();
  const sourceDevices = await getStaticSourceDevices();
  const scope = fitbitResponse?.scope?.split(' ') || []
  const trackers = []
  scope.forEach(tr => {
    const trackerIds = trackersFitbitScopeMap?.[tr] || [];
    trackerIds.forEach(tId => trackers.push({trackerId: Number(tId), isEnabled: true}))
  })

  const document = {
    userId,
    newDevice: {
      id: sourceDevices[sources.Fitbit.id]?.[0]?.id,
      trackers, isConnected: true
    },
    updatedAt: new Date().toISOString()
  };
  await devicesService.connectNewDevices(userId, document, true, accessToken);  
}

async function getFitbitAccessToken(userId) {
  const sources = await getStaticSources();
  log.info(`Capturing fitbit access token for userId: ${userId}`)
  const userDetails = await userService.getUserDetails(userId, sources.Fitbit.id);
  if (userDetails && userDetails.accessToken) {
    const currentDate = new Date();
    const expiryDate = new Date(userDetails.expiresAt);
    if (expiryDate.getTime() >= currentDate.getTime()) {
      log.info("Returning current access token")
      return userDetails.accessToken;
    } else {
      log.info("Generating from refresh token")
      try {
        const fitbitResponse = await fitbit.auth.getRefreshToken(
          userDetails.refreshToken
        );
        await storeFitbitDetails(userId, fitbitResponse);
        log.info(`Successfully generated token for userId: ${userId}`, fitbitResponse.access_token)
        return fitbitResponse.access_token;
      } catch (error) {
        const errorStatus = error.response?.status;
        if (errorStatus >= 400 && errorStatus < 500) {
            await revokeFitbitToken(userId, false);
        }
        log.warn(`Error retreiving Fitbit access token for userId: ${userId}`)
        return null;
      }
    }
  }
  log.warn(`Failed to fetch user details for userId: ${userId}`)
  return null;
}

async function getOAuthUrl(userId, isUpdateLastSynced = true) {
  // Create State and Register User in OpenSearch
  const state = uuidv4();
  const sources = await getStaticSources();
  const date = new Date().toISOString();
  const deviceId = await devicesService.getDefaultDeviceIdBySource(sources.Fitbit.id);
  const userData = {
    state,
    source: sources.Fitbit.name,
    sourceId: sources.Fitbit.id,
    trackers: sources.Fitbit.trackers,
    createdAt: date,
    updatedAt: date,
  };

  if (isUpdateLastSynced) {
    userData.devices = {
      [deviceId]: {
        lastSynced: date,
      },
    };
    userData.lastSynced = date;
  }

  await userService.upsertUserDetails(userId, userData, sources.Fitbit.id);

  const authorizeUrl =
    config.fitbit.authorize_url +
    "?response_type=code&scope=weight%20location%20settings" +
    "%20stress%20respiratory_rate%20activity%20social%20heartrate%20mindfulness%20profile%20cardio_fitness" +
    "%20sleep%20oxygen_saturation%20nutrition%20electrocardiogram%20temperature&client_id=" +
    config.fitbit.client_id +
    "&state=" +
    state + "&prompt=consent";

  return authorizeUrl;
}

module.exports = {
  getMetadata,
  callback,
  getNotification,
  postNotification,
  ManualSync,
  syncFitbitData,
  revokeFitbitToken,
  getFitbitAccessToken,
  getOAuthUrl,
}
