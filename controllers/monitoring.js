const { userService } = require("../service/users");
const { getStaticTrackers, getStaticTrackerDefaultTargetsMapping } = require("../utils/staticData");
const logsService = require("../service/logs");
const { deepCopy } = require("../utils/helpers");
const { getConnectedUserList } = require("../utils/aws");
const { trackersService } = require("../service/trackers");

module.exports.getUserCapturedCount = async function (req, res, next) {
  const userId = req.query?.doctor_guid || req.decoded.user_guid; //Query applicable only for BUSER
  if (!userId) {
    return next({ message: "user_id missing in request", statusCode: 400 });
  }
  let endTime = new Date(), startTime = new Date();
  if (req.query?.startDate && req.query?.endDate) {
    startTime = new Date(req.query?.startDate);
    endTime = new Date(req.query?.endDate);
  } else {
    const duration = parseInt(req.query?.duration) || 2;
    startTime.setDate(startTime.getDate() - duration);
  }
  const connectedUsers = await getConnectedUserList(userId);
  if(!connectedUsers){
    return next({ success: false, message: "Error fetching connected users",});
  }
  const noOfConnectedUsers = connectedUsers.length;
  const capturedCountsPerTrackerID = {}, uncapturedCountsPerTrackerID = {};
  
  const defaultDevicesPerUser = {};
  await Promise.all(connectedUsers.map(async (userId) => {
    const response = await trackersService.getAllDefaultTrackers(userId);
    defaultDevicesPerUser[userId] = response?.defaultDevices || [];
  }));

  await Promise.all(
    Object.keys(logsService.trackerMap).map(async (id) => {
      if(id != 1){ //id == 1 belongs to meal Log
        const tracker = logsService.trackerMap[id];
        const capturedUserList = await userService.getUsersIfCaptured(connectedUsers, id, tracker.indexName, startTime.toISOString(), endTime.toISOString(), true, defaultDevicesPerUser);
        const capturedUserCount = capturedUserList.length;
        capturedCountsPerTrackerID[id] = capturedUserCount;
        uncapturedCountsPerTrackerID[id] = noOfConnectedUsers - capturedUserCount;      
      }
    })
  );
  const trackers = await getStaticTrackers();
  const trackerDefaultTargetsMapping = await getStaticTrackerDefaultTargetsMapping();
  let data = deepCopy(trackers);
  data.forEach((d) => {
    d.subCategories = d.subCategories.map((tr) => {
      let result = { ...tr };
      result = { ...result, capturedUserCount: capturedCountsPerTrackerID[tr.id], };
      result = { ...result, uncapturedUserCount: uncapturedCountsPerTrackerID[tr.id], };
      result.defaultTarget = trackerDefaultTargetsMapping[tr.id];
      return result;
    });
  });

  return res.status(200).json({
      success: true,
      data,
  });
};

module.exports.getUsersIfCaptured = async function (req, res, next) {
  const userId = req.query?.doctor_guid || req.decoded.user_guid; //Query applicable only for BUSER
  const trackerId = req.params?.trackerId;
  const isLogCaptured = req.query?.isLogCaptured;

  if (!userId) {
    return next({ message: "user_id missing in request", statusCode: 400 });
  }
  const tracker = logsService.trackerMap[trackerId]
  if (!tracker) {
    return next({ message: "Invalid trackerId", statusCode: 400 });
  }
  if (!isLogCaptured || !['true','false'].includes(isLogCaptured)) {
    return next({ message: "Captured flag missing in request or its value is incorrect", statusCode: 400 });
  }

  let endTime = new Date(), startTime = new Date();
  if (req.query?.startDate && req.query?.endDate) {
    startTime = new Date(req.query?.startDate);
    endTime = new Date(req.query?.endDate);
  } else {
    const duration = parseInt(req.query?.duration) || 2;
    startTime.setDate(startTime.getDate() - duration);
  }
  const connectedUsers = await getConnectedUserList(userId);
  if(!connectedUsers){
    return next({ success: false, message: "Error fetching connected users",});
  }
  const defaultDevicesPerUser = {};
  await Promise.all(connectedUsers.map(async (userId) => {
    const response = await trackersService.getAllDefaultTrackers(userId);
    defaultDevicesPerUser[userId] = response?.defaultDevices || [];
  }));
  const userIdList = await userService.getUsersIfCaptured(connectedUsers, trackerId, tracker.indexName, startTime.toISOString(), endTime.toISOString(), isLogCaptured == "true" ? true : false, defaultDevicesPerUser);
  if (userIdList) {
    res.status(200).json({
      success: true,
      result: { userIdList },
    });
  } else {
    res.status(200).json({
      success: false,
      message: `Something went wrong!`,
    });
  }
};
