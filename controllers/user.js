const { userService } = require("../service/users");

module.exports.getUserDetail = async function (req, res, next) {
  const { user_guid: userGuid, user_id: userId } = req.decoded;
  const sourceId = req?.query?.sourceId;

  if (!userId || !userGuid) {
    return next({ message: "user_id missing in request", statusCode: 400 });
  }
  if (!sourceId) {
    return next({ message: "sourceId missing in request query", statusCode: 400 });
  }
  const userDetails = await userService.getUserDetails(userGuid, sourceId);

  if (userDetails) {
    res.status(200).json({
      success: true,
      data: { userDetails },
    });
  } else {
    res.status(200).json({
      success: false,
      message: `User is not connected with the provided source Id`,
    });
  }
};
