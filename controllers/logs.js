const { asyncHandler, parsePaginationParams, getNextPrevPagination, roundOffNumber, getDayTimeRange } = require("../utils/helpers");
const { trackersService } = require("../service/trackers");
const { devicesService } = require("../service/devices")
const { logAggregation } = require("../logAggregation");
const logsService = require("../service/logs");
const { isNumber } = require("lodash");
const { formatLogs, supportedTrackerIds: formatLogsSupportedTrackerIds } = require("../utils/trackerLogsSchema");
const { userService } = require("../service/users");
const { getStaticSourcesArray, getStaticDevices } = require("../utils/staticData");
const { log: logger } = require("../utils/logger");
const { upsertUTCOffsetValue } = require("../service/userProfile");
const { addLogsToQueue } = require("../utils/trackersIngestion");

const MANUAL_ENTRY_DEVICE_ID = -1;
const BGM_TRACKER_ID = 7; // Blood Glucose Management
const CGM_TRACKER_ID = 8; // Continuous Blood Glucose Management

module.exports.getLogById = asyncHandler(async function (req, res, next) {
  const { user_guid: userId } = req.decoded;
  const { trackerId, logId } = req.params;
  if (!userId || !trackerId || !logId) {
    return next({ message: "Missing required params in request", statusCode: 400 });
  }
  let UTCOffSetMin = 0;
  if (req.headers?.utcoffsetmin !== undefined) {
    UTCOffSetMin = Number(req.headers.utcoffsetmin);
    upsertUTCOffsetValue(userId, UTCOffSetMin);
  }

  const tracker = logsService.trackerMap[trackerId]
  if (!tracker) {
    return next({ message: "Invalid trackerId", statusCode: 400 });
  }
  let log = await logsService.getDataByLogId(tracker.indexName, userId, logId);
  if (!log) {
    return res.status(404).json({ success: false, message: 'Log not found' });
  }
  const devices = await getStaticDevices();
  let dv = devices.filter(dv => dv.id == log.deviceId)?.[0]
  log = dv ? { ...log, deviceImageUrl: dv?.detail?.imageUrl } : log
  res.status(200).json({ success: true, data: log });
});

module.exports.getLogs = asyncHandler(async function (req, res, next) {
  const userId = req.query?.user_guid || req.decoded.user_guid;
  const trackerId = req.params?.trackerId;
  let UTCOffSetMin = 0;
  if (req.headers?.utcoffsetmin !== undefined) {
    UTCOffSetMin = Number(req.headers.utcoffsetmin);
    upsertUTCOffsetValue(userId, UTCOffSetMin);
  }

  if (!userId) {
    return next({ message: "user_id missing in request", statusCode: 400 });
  }
  const tracker = logsService.trackerMap[trackerId]
  if (!tracker) {
    return next({ message: "Invalid trackerId", statusCode: 400 });
  }
  let type = req.query?.paginate == 'true' ? 'pagination' : 'duration';
  if (!['duration', 'pagination'].includes(type)) {
    type = 'duration'
  }

  let logs = []
  switch (type) {
    case 'duration': {
      let endTime = new Date(), startTime = new Date();
      if (req.query?.startDate && req.query?.endDate) {
        startTime = new Date(req.query?.startDate)
        endTime = new Date(req.query?.endDate)
      }
      else {
        let duration = parseInt(req.query?.duration) || 3;
        duration -= 1; // Current day is day 1
        startTime.setDate(startTime.getDate() - duration)
      }
      logs = await logsService.getAllLogsByDateRange(tracker.indexName, userId, startTime.toISOString(), endTime.toISOString());
      break;
    }
    case 'pagination': {
      var { limit, page } = parsePaginationParams(req.query);
      from = limit * (page - 1)
      logs = await logsService.getAllLogsByPagination(tracker.indexName, userId, from, limit);
      break;
    }
  }

  let resp = await trackersService.getAllDefaultTrackers(userId);
  defaultDevice = resp?.defaultDevices?.filter(x => x.trackerId == trackerId)?.[0]

  if (defaultDevice) {
    logs = logs.filter(log => 
      [defaultDevice.deviceId, MANUAL_ENTRY_DEVICE_ID].includes(log.deviceId)
    )
  }
  const devices = await getStaticDevices();
  logs = logs.map(log => {
    let dv = devices.filter(dv => dv.id == log.deviceId)?.[0]
    return dv ? { ...log, deviceImageUrl: dv?.detail?.imageUrl } : log
  })

  logs = await logAggregation(trackerId, logs, UTCOffSetMin)

  let response = { success: true, data: logs }
  if (type == 'pagination') {
    response = {
      ...response, 
      ...getNextPrevPagination(`/trackers/api/v1/${trackerId}`, page, limit, logs?.length)
    }
  }
  res.status(200).json(response);
});

module.exports.postLogs = async function (req, res, next) {
  const { user_guid: userId } = req.decoded;
  const trackerId = req.params?.trackerId;
  if (!userId) {
    return next({
      message: "Invalid params: userId is mandatory field.",
      statusCode: 400,
    });
  }
  let UTCOffSetMin = 0;
  if (req.headers?.utcoffsetmin !== undefined) {
    UTCOffSetMin = Number(req.headers.utcoffsetmin);
    upsertUTCOffsetValue(userId, UTCOffSetMin);
  }

  const tracker = logsService.trackerMap[trackerId]
  if (!tracker) {
    return next({ message: "Invalid trackerId", statusCode: 400 });
  }
  const sources = await getStaticSourcesArray();
  const now = new Date()
  const log = req.body
  const deviceId = log.deviceId !== undefined ? log.deviceId : MANUAL_ENTRY_DEVICE_ID;
  const devices = await getStaticDevices();
  const device = devices.filter(dv => dv.id == deviceId)?.[0]
  if (!device) return next({ message: "Invalid deviceId", statusCode: 400 });
  const deviceName = log?.deviceName || device.name
  const sourceId = device.sourceId
  const source = sources.filter(s => s.id == sourceId)?.[0]
  const sourceName = source.name

  // Reformat logs: Sleep, BP, etc
  const formattedLogs = formatLogs(trackerId, log)
  const trackerIdLogsMapping = {};
  for (const formattedLog of formattedLogs) {
    const newDoc = {
      userId,
      ...formattedLog,
      logId: formattedLog.logId || null,
      sourceId,
      sourceName,
      deviceId,
      deviceName,
      createdAt: now.toISOString(),
      updatedAt: now.toISOString(),
    };
  
    const valid = tracker.validate(newDoc);
    if (!valid) continue;
  
    if (newDoc.value && isNumber(newDoc.value)) {
      newDoc.value = roundOffNumber(newDoc.value);
    }
  
    trackerIdLogsMapping[trackerId] ||= [];
    trackerIdLogsMapping[trackerId].push(newDoc);
  }
  logger.info(`trackerIdLogsMapping: ${JSON.stringify(trackerIdLogsMapping)}`);
  const isPushedToSQS = await addLogsToQueue(userId, trackerIdLogsMapping);
  if (isPushedToSQS) {
    return res.status(200).json({
      success: true,
      message: `Log entries received`,
    });
  }
  return res.status(500).json({
    success: false,
    message: `Something went wrong while processing the logs`,
  });
};

module.exports.bulkPost = async function (req, res, next) {
  console.time('bulkPost whole time');
  const { user_guid: userId } = req.decoded;
  const overwriteIncrementalLogs = (req.query?.overwrite == 'true') ? true : false;
  if (!userId) {
    return next({
      message: "Invalid params: userId is mandatory field.",
      statusCode: 400,
    });
  }
  let UTCOffSetMin = 0;
  if (req.headers?.utcoffsetmin !== undefined) {
    UTCOffSetMin = Number(req.headers.utcoffsetmin);
    upsertUTCOffsetValue(userId, UTCOffSetMin);
  }
  console.time('getStaticSourcesArray');
  const sources = await getStaticSourcesArray();
  console.timeEnd('getStaticSourcesArray');
  console.time(`preProcessingLogs`);
  const now = new Date();
  const trackerIdLogsMapping = {};
  const trackerIds = Object.keys(req.body);
  const trackerIdDeviceIdMapping = {};
  for (const id of trackerIds) {
    const trackerId = parseInt(id) || -1;
    const logs = req.body[trackerId];
    const tracker = logsService.trackerMap[trackerId];
    if (!tracker) {
      logger.warn(`Invalid trackerId: ${trackerId}`);
      continue;
    }
  
    const devices = await getStaticDevices();
    if (overwriteIncrementalLogs) {
      let formattedLogs = logs;
      if (formatLogsSupportedTrackerIds.includes(trackerId.toString())) {
        formattedLogs = formatLogs(trackerId, logs[0]);
      }
  
      const { startTime, endTime } = getStartAndEndTime(formattedLogs, UTCOffSetMin);
      const uniqueDeviceIds = [...new Set(formattedLogs.map(log => log.deviceId ?? MANUAL_ENTRY_DEVICE_ID))];
  
      await Promise.all(uniqueDeviceIds.map(async deviceId => {
        const deletedLogs = await logsService.deleteLogsByDeviceId(tracker.indexName, userId, startTime, endTime, deviceId);
        trackerIdLogsMapping[trackerId] = deletedLogs;
        logger.info(`Overwrite = true, deletedLogIds: ${deletedLogs.map(l => l.id)}, startTime: ${startTime}, endTime: ${endTime}, deviceId: ${deviceId}, trackerId: ${trackerId}`);
  
        if (trackerId === BGM_TRACKER_ID && deletedLogs.length > 0) {
          const { indexName: cgmIndexName } = logsService.trackerMap[CGM_TRACKER_ID];
          const deletedCGMLogs = await logsService.deleteLogsByDeviceId(cgmIndexName, userId, startTime, endTime, deviceId);
          trackerIdLogsMapping[CGM_TRACKER_ID] = deletedCGMLogs;
          logger.info(`Overwrite = true, deletedLogIds: ${deletedCGMLogs.map(l => l.id)}, startTime: ${startTime}, endTime: ${endTime}, deviceId: ${deviceId}, trackerId: ${CGM_TRACKER_ID}`);
        }
      }));
    }
    for (const log of logs) {
      try {
        const deviceId = log.deviceId ?? MANUAL_ENTRY_DEVICE_ID;
        const device = devices.find(d => d.id == deviceId);
        if (!device) {
          logger.warn(`Invalid deviceId: ${deviceId}`);
          continue;
        }
        const sourceId = device.sourceId;
        const source = sources.find(s => s.id == sourceId);
        if (!source) {
          logger.warn(`Invalid sourceId: ${sourceId}`);
          continue;
        }
    
        const deviceName = log.deviceName || device.name;
        const sourceName = source.name;
    
        const formattedLogs = formatLogs(trackerId, log);
        for (const formattedLog of formattedLogs) {
          try {
            const newDoc = {
              userId,
              ...formattedLog,
              logId: formattedLog.logId || null,
              sourceId,
              sourceName,
              deviceId,
              deviceName,
              createdAt: now.toISOString(),
              updatedAt: now.toISOString(),
            };
    
            if (!tracker.validate(newDoc)) {
              logger.warn(`Validation failed for log: ${JSON.stringify(newDoc)}`);
              continue;
            }
    
            if (newDoc.value && isNumber(newDoc.value)) {
              newDoc.value = roundOffNumber(newDoc.value);
            }
            trackerIdDeviceIdMapping[trackerId] = newDoc.deviceId;
            trackerIdLogsMapping[trackerId] ||= [];
            trackerIdLogsMapping[trackerId].push(newDoc);
          } catch (err) {
            logger.error(`Error processing formatted log: ${err.message}`);
          }
        }
      } catch (err) {
        logger.error(`Error processing log: ${err.message}`);
      }
    }
  }
  console.timeEnd(`preProcessingLogs`);
  // logger.info(`trackerIdLogsMapping: ${JSON.stringify(trackerIdLogsMapping)}`);
  const isPushedToSQS = await addLogsToQueue(userId, trackerIdLogsMapping);

  logger.info(`Calling updateNewDeviceStatus: ${JSON.stringify(trackerIdDeviceIdMapping)}`);
  updateNewDeviceStatus(userId, trackerIdDeviceIdMapping).catch((err) => {
    logger.error("Error in updateNewDeviceStatus():", JSON.stringify(err));
  });

  console.timeEnd('bulkPost whole time');
  if (isPushedToSQS) {
    return res.status(200).json({
      success: true,
      message: `Log entries received`,
    });
  }
  return res.status(500).json({
    success: false,
    message: `Something went wrong while processing the logs`,
  });
};

async function updateNewDeviceStatus(userId, trackerIdDeviceIdMapping) {
  for (const [trackerId, deviceId] of Object.entries(trackerIdDeviceIdMapping)) {
    if (deviceId == MANUAL_ENTRY_DEVICE_ID) continue;

    const now = new Date();
    const newDoc = {
      userId,
      newDevice: {
        id: deviceId,
        trackers: [{ trackerId: Number(trackerId), isEnabled: true }],
        isConnected: true,
      },
      createdAt: now.toISOString(),
      updatedAt: now.toISOString(),
    };

    try {
      await devicesService.connectNewDevices(userId, newDoc, false);
    } catch (err) {
      logger.error(`Error connecting new device (deviceId: ${deviceId}, trackerId: ${trackerId}):`, JSON.stringify(err));
    }
  }
}

module.exports.deleteLogById = async function (req, res, next) {
  const { user_guid: userId } = req.decoded;
  const { trackerId, logId } = req.params;
  if (!userId || !trackerId || !logId) {
    return next({ message: "Missing required params in request", statusCode: 400 });
  }
  const tracker = logsService.trackerMap[trackerId]
  if (!tracker) {
    return next({ message: "Invalid trackerId", statusCode: 400 });
  }

  let log = await logsService.deleteLogById(tracker.indexName, userId, logId);
  if (!log) {
    return res.status(200).json({ success: false, message: 'Something went wrong!'});
  }

  const computeTargetAchievementFlag = await logsService.postLogUpsert(userId, { [trackerId]: [log] });
  logger.info(`postLogUpsert, computeTargetAchievementFlag: ${computeTargetAchievementFlag} for userId: ${userId}, trackerId: ${trackerId}`);
  
  return res.status(200).json({ success: true, message: 'Log deleted' });
};

module.exports.updateLogById = async function (req, res, next) {
  const { user_guid: userId } = req.decoded;
  const { trackerId, logId } = req.params;
  if (!userId || !trackerId || !logId) {
    return next({ message: "Missing required params in request", statusCode: 400 });
  }
  const tracker = logsService.trackerMap[trackerId]
  if (!tracker) {
    return next({ message: "Invalid trackerId", statusCode: 400 });
  }
  const sources = await getStaticSourcesArray();
  const log = req.body
  const now = new Date()
  const devices = await getStaticDevices();
  const deviceId = log?.deviceId || MANUAL_ENTRY_DEVICE_ID
  const device = devices.filter(dv => dv.id == deviceId)?.[0]
  const deviceName = log?.deviceName || device.name
  if (!device) return next({ message: "Invalid deviceId", statusCode: 400 });
  
  const sourceId = device.sourceId
  const sourceName = sources.filter(s => s.id == sourceId)?.[0].name

  const newDoc = {
    userId, ...log,
    sourceId,sourceName, deviceId, deviceName,
    createdAt: now.toISOString(),
    updatedAt: now.toISOString()
  };
  const valid = tracker.validate(newDoc);
  if (!valid) {
    return next({ message: 'Validation Failed', error: tracker.validate.errors, statusCode: 400 });
  }  

  let id = await logsService.updateLogById(trackerId, tracker.indexName, newDoc, userId, logId);
  if (!id) {
    return res.status(200).json({ success: false, message: 'Something went wrong!'});
  }

  const computeTargetAchievementFlag = await logsService.postLogUpsert(userId, { [trackerId]: [newDoc] });
  logger.info(`postLogUpsert, computeTargetAchievementFlag: ${computeTargetAchievementFlag} for userId: ${userId}, trackerId: ${trackerId}`);
  return res.status(200).json({ success: true, message: 'Log updated', id });
};

function getStartAndEndTime(logs, UTCOffSetMin) {
  if (!logs || logs.length === 0) return { startTime: null, endTime: null };
  const timestamps = logs.map(log => new Date(log.timestamp));
  const { startTime } = getDayTimeRange(UTCOffSetMin, new Date(Math.min(...timestamps)).toISOString());
  const { endTime } = getDayTimeRange(UTCOffSetMin, new Date(Math.max(...timestamps)).toISOString());
  return { startTime, endTime };
}
