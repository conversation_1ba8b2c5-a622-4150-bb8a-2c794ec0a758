module.exports = {
  OS_HOST:
    "https://search-healthtechgate-rcnre4hj6m3vaf4qszvvaepdxi.us-east-1.es.amazonaws.com",
  REGION: "us-east-1",
  SERVER_URL: "https://services.healthtechgate.com",
  PORT: 443,
  BASE_PATH: "Trackers",
  fitbit: {
    authorize_url: "https://www.fitbit.com/oauth2/authorize",
    api_base_url: "https://api.fitbit.com",
    client_id: "238H9K",
    client_secret: "8e524a1ab77815e19704b2b33e39d170",
    subscriberId: "1",
    verificationCode:
      "6aab42a32d688d52a325c6219ee86ae7d062dc1946b4ce72687ee410a8eb4080",
  },
  dexcom: {
    authorize_url: "https://api.dexcom.com/v2/oauth2/login",
    api_base_url: "https://api.dexcom.com",
    client_id: "zAEVOOmf5GT3I799A4oE8InVJ4tktq57",
    client_secret: "pcp40OZs0AvcIoUw",
    authorize_redirect: "https://services.healthtechgate.com/trackers/dexcom/callback"
  },
  oura: {
    authorize_url: "https://cloud.ouraring.com/oauth/authorize",
    api_base_url: "https://api.ouraring.com",
    client_id: "3YQO3BMVFR3TF55R",
    client_secret: "V3MB5DQ66BBNGXO4IHEH6NZSWGQJ6IK6",
    authorize_redirect: "https://services.healthtechgate.com/trackers/oura/callback"
  },
  AWS: {
    sqsURL:
      "https://sqs.us-east-1.amazonaws.com/188667991403/trackers-notification-prod.fifo",
    USER_CONNECTIONS_LAMBDA: "user-connections",
    PUSH_NOTIFICATIONS_LAMBDA: "push-notifications",
    TRACKERS_STATIC_DATA_LAMBDA: "trackers-static",
    TARGET_COMPUTATION_SQS_URL: "https://sqs.us-east-1.amazonaws.com/188667991403/target-achievements.fifo",
    TRACKERS_INGESTION_SQS_URL: "https://sqs.us-east-1.amazonaws.com/188667991403/trackers-ingestion.fifo",
    TRACKERS_INGESTION_LOGS_S3_BUCKET: "twentydeg",
  },
  MONOLITH: {
    SERVER_URL: "https://api.healthtechgate.com",
  },
  dynalinks: {
    base_url: "https://20deg.dynalinks.app",
    apiKey: "YeyqACqKNUMA7ySRy6hGnt1W",
    forms: "https://20deg.dynalinks.app/forms",
    wellness_score: "https://20deg.dynalinks.app/wellness-score",
    device_connection: "https://20deg.dynalinks.app/devices", 
    device_permission: "https://20deg.dynalinks.app/permissions", 
  },
  marketingWebsite: "https://www.20deg.com",
};
