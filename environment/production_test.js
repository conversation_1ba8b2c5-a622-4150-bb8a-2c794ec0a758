module.exports = {
  OS_HOST:
    "https://search-healthtechgate-provider-o2nranqpg5ehoncqitfnu7fz2a.us-east-1.es.amazonaws.com",
  REGION: "us-east-1",
  SERVER_URL: "https://services.dev.healthtechgate.com",
  PORT: 443,
  BASE_PATH: "Trackers",
  fitbit: {
    authorize_url: "https://www.fitbit.com/oauth2/authorize",
    api_base_url: "https://api.fitbit.com",
    client_id: "2392WM",
    client_secret: "********************************",
    subscriberId: "1",
    verificationCode: "67e2f0c24796b838256927d5eba4f0a3417f7c06af14681f8bcd87033fdb8047",
  },
  dexcom: {
    authorize_url: "https://sandbox-api.dexcom.com/v2/oauth2/login",
    api_base_url: "https://sandbox-api.dexcom.com",
    client_id: "********************************",
    client_secret: "i7OZWT13Zy1GCVTD",
    authorize_redirect: "https://services.dev.healthtechgate.com/trackers/dexcom/callback"
  },
  oura: {
    authorize_url: "https://cloud.ouraring.com/oauth/authorize",
    api_base_url: "https://api.ouraring.com",
    client_id: "7D6HN6ZQYHSPKN67",
    client_secret: "LTVNFPGPYZINK3RWCAVXJAP2POCVR4NP",
    authorize_redirect: "https://services.dev.healthtechgate.com/trackers/oura/callback"
  },
  AWS: {
    sqsURL:
      "https://sqs.us-east-1.amazonaws.com/188667991403/trackers-notification.fifo",
    USER_CONNECTIONS_LAMBDA: "user-connections-dev",
    PUSH_NOTIFICATIONS_LAMBDA: "push-notifications-dev",
    TRACKERS_STATIC_DATA_LAMBDA: "trackers-static-dev",
    TARGET_COMPUTATION_SQS_URL: "https://sqs.us-east-1.amazonaws.com/188667991403/target-achievements-dev.fifo",
    TRACKERS_INGESTION_SQS_URL: "https://sqs.us-east-1.amazonaws.com/188667991403/trackers-ingestion-dev.fifo",
    TRACKERS_INGESTION_LOGS_S3_BUCKET: "twentydeg-dev",
  },
  MONOLITH: {
    SERVER_URL: "https://api.dev.healthtechgate.com",
  },
  dynalinks: {
    base_url: "https://20deg.dynalinks.app",
    apiKey: "YeyqACqKNUMA7ySRy6hGnt1W",
    forms: "https://20deg.dynalinks.app/forms",
    wellness_score: "https://20deg.dynalinks.app/wellness-score",
    device_connection: "https://20deg.dynalinks.app/devices", 
    device_permission: "https://20deg.dynalinks.app/permissions", 
  },
  marketingWebsite: "https://www.20deg.com",
};
