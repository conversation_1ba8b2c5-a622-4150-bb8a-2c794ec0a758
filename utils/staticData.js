const AWS = require("aws-sdk");
AWS.config.update({ region: "us-east-1" });
const lambda = new AWS.Lambda({ apiVersion: "2015-03-31" });
const { config } = require("../environment/index");
const { log } = require("./logger");
const { checkIfAnyEmpty, deepCopy } = require("../utils/helpers");

let staticData = '{"targetsMap":{},"trackerMap":[],"devices":[],"sources":{},"sourcesArray":[],"sourceSummary":{},"sourceDevices":{},"devicesWithoutManualEntry":[],"sourcesDevicesMapping":{}}';

async function fetchStaticData() {
  try {
    const params = {
      FunctionName: config.AWS.TRACKERS_STATIC_DATA_LAMBDA,
      InvocationType: "RequestResponse",
      LogType: "Tail",
      Qualifier: "provisioned",
    };
    return new Promise(async (resolve, reject) => {
      lambda.invoke(params, (error, data) => {
        if (error) {
          log.error(
            "Error while calling Lambda function",
            JSON.stringify(error)
          );
          resolve(null);
        }
        var payload = JSON.parse(data.Payload);
        resolve(payload.body);
      });
    });
  } catch (error) {
    log.warn(`Failed to fetch staticData from trackers`);
    log.warn(JSON.stringify(error));
    return null;
  }
}

async function getStaticData() {
  let staticDataJson = JSON.parse(staticData || "{}");
  if (!checkIfAnyEmpty(staticDataJson)) {
    return staticDataJson;
  }
  const responseString = await fetchStaticData();
  const response = JSON.parse(responseString);
  if (response?.success) {
    const { targetsMap, trackerMap, devices, sources } = response.data;
    const sourcesArray = await getStaticSourcesArray(sources);
    const sourceSummary = await getStaticSourceSummary(deepCopy(sources));
    const devicesWithoutManualEntry = await getStaticDevicesWithoutManualEntry(devices);
    const sourceDevices = await getStaticSourceDevices(devices);
    const sourcesDevicesMapping = await getStaticSourcesDevicesMapping(devices);
    const trackerDefaultTargetsMapping = await getStaticTrackerDefaultTargetsMapping(targetsMap);
    Object.assign(staticDataJson, {
      targetsMap,
      trackerMap,
      devices,
      sources,
      sourcesArray,
      sourceSummary,
      devicesWithoutManualEntry,
      sourceDevices,
      sourcesDevicesMapping,
      trackerDefaultTargetsMapping,
    });
    staticData = JSON.stringify(staticDataJson);
    log.info(response?.message || `Successfully fetched static data`);
  } else {
    log.warn(`Success: false, Failed to fetch staticData from trackers`);
  }
  return staticDataJson;
}

async function getStaticTargetsMap() {
  const staticData = await getStaticData();
  return staticData?.targetsMap || {};
}

async function getStaticTrackers() {
  const staticData = await getStaticData();
  return staticData?.trackerMap || [];
}

async function getStaticSources() {
  const staticData = await getStaticData();
  const sources = staticData?.sources || {};
  return sources;
}

async function getStaticDevices() {
  const staticData = await getStaticData();
  const devices = staticData?.devices || [];
  return devices;
}

// ----- modified jsons -----
async function getStaticSourcesArray(sources) {
  if (!sources) {
    const data = await getStaticData();
    return data?.sourcesArray || {};
  }
  const sourcesArray = Object.keys(sources).map((s) => sources[s]);
  return sourcesArray;
}

async function getStaticSourceSummary(sources) {
  if (!sources) {
    const data = await getStaticData();
    return data?.sourceSummary || {};
  }
  let sourceSummary = {};
  Object.keys(sources).map((sourceId) => {
    const id = sources[sourceId].id;
    sourceSummary[id] = sources[sourceId];
    delete sourceSummary[id].trackers;
  });
  return sourceSummary;
}

async function getStaticDevicesWithoutManualEntry(devices) {
  if (!devices) {
    const data = await getStaticData();
    return data?.devicesWithoutManualEntry || {};
  }
  const devicesWithoutManualEntry = devices.filter(x => x.id != -1);
  return devicesWithoutManualEntry;
}

async function getStaticSourceDevices(devices) {
  if (!devices) {
    const data = await getStaticData();
    return data?.sourceDevices || {};
  }
  const sourceDevices = devices.reduce((acc, dev) => {
    const sourceId = dev.sourceId.toString();
    acc[sourceId] = acc[sourceId] || [];
    acc[sourceId].push(dev);
    return acc;
  }, {});
  return sourceDevices;
}

async function getStaticSourcesDevicesMapping(devices) {
  if (!devices) {
    const data = await getStaticData();
    return data?.sourcesDevicesMapping || {};
  }
  const sourcesDevicesMapping = devices.reduce((obj, { sourceId, id }) => ((obj[sourceId] ||= []).push(id), obj), {});
  return sourcesDevicesMapping;
}

async function getStaticTrackerDefaultTargetsMapping(targetsMap) {
  if (!targetsMap) {
    const data = await getStaticData();
    return data?.trackerDefaultTargetsMapping || {};
  }
  let trackerDefaultTargetsMapping = {};
  Object.keys(targetsMap).map((targetId) => {
    const targetDoc = targetsMap[targetId];
    if (targetsMap[targetId]?.isDefault)
      trackerDefaultTargetsMapping[targetDoc.trackerIds[0]] = Number(targetId);
  });
  return trackerDefaultTargetsMapping;
}

module.exports = {
  getStaticData,
  staticData,
  getStaticTargetsMap,
  getStaticTrackers,
  getStaticSources,
  getStaticDevices,
  getStaticSourcesArray,
  getStaticSourceSummary,
  getStaticDevicesWithoutManualEntry,
  getStaticSourceDevices,
  getStaticSourcesDevicesMapping,
  getStaticTrackerDefaultTargetsMapping,
};
