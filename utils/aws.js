const AWS = require("aws-sdk");
AWS.config.update({ region: "us-east-1" });

const { config } = require("../environment/index");
const { log } = require("../utils/logger");

const sqs = new AWS.SQS({ apiVersion: "2012-11-05", region:'us-east-1'});
const lambda = new AWS.Lambda({apiVersion: '2015-03-31'});

async function sendSQSMessage(message) {
  const params = {
    MessageAttributes: {},
    MessageBody: message,
    QueueUrl: config.AWS.sqsURL,
    MessageGroupId: 'TrackersNotification'
  };
  try {
      await sqs.sendMessage(params).promise();
      return true;
  } catch (err) {
      log.info("Error sending message to SQS", JSON.stringify(err));
      return false;
  }
}

async function getUserConnections(userGuid) {
  const params = {
    FunctionName: config.AWS.USER_CONNECTIONS_LAMBDA,
    InvocationType: 'RequestResponse',
    LogType: 'Tail',
    Payload: JSON.stringify({"user_guid": userGuid})
  };
  return new Promise(async (resolve, reject) => {
      lambda.invoke(params, (error, data) => {
        if (error) {
          log.error("Error while calling Lambda function", JSON.stringify(error));
          resolve(null);
        }
        var payload = JSON.parse(data.Payload);
        resolve(payload.body);
      });
  });
};

async function getConnectedUserList(userGuid) {
  try{
    const response = await getUserConnections(userGuid);
    const resultData = JSON.parse(response);
    const connectedUserIds = resultData.result.map((item) => item.user_guid);
    return connectedUserIds;
  }
  catch (err){
    log.info(`Error fetching connected Users for userId: ${userGuid}`, JSON.stringify(err));
    return null;
  }
};

async function sendPushNotification(userGuid, notificationUserGuid, alert, title, type, customData = {} ) {
  try {
    const params = {
      FunctionName: config.AWS.PUSH_NOTIFICATIONS_LAMBDA,
      InvocationType: 'Event',
      LogType: 'Tail',
      Payload: JSON.stringify({"user_guid": userGuid, "notification_user_guid": notificationUserGuid,
          "alert": alert, "title": title, "type": type, "custom_data": customData })
    };
    log.info(`Calling Notification Lambda, ${JSON.stringify(params)}`);
    const data = await lambda.invoke(params).promise();
    log.info(`Notification Lambda Response, ${JSON.stringify(data)}`);
    return true;
  }
  catch (error) {
    log.warn(`Error calling sendPushNotification, ${JSON.stringify(error)}`);
    return false;
  }
};

async function sendTargetComputationSQSMessage(message) {
  const params = {
    MessageAttributes: {},
    MessageBody: message,
    QueueUrl: config.AWS.TARGET_COMPUTATION_SQS_URL,
    MessageGroupId: 'TargetComputation',
  };
  try {
    log.info(`targetComputationSQSMessage: ${message}`);
    const resp = await sqs.sendMessage(params).promise();
    return true;
  } catch (err) {
    log.info("Error sending message to Target Computation SQS", JSON.stringify(err));
    return false;
  }
}

module.exports = {
  sendSQSMessage,
  getUserConnections,
  getConnectedUserList,
  sendPushNotification,
  sendTargetComputationSQSMessage,
};
