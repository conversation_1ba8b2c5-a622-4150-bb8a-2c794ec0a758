const axios = require("axios").default;
const { config } = require("../environment/index");
const { log } = require("./logger");
const baseUrl = `${config.SERVER_URL}`;

async function assignFirstTargetForm(userId) {
  try {
    const xAPIKey = config.xAPIKey;
    const reqConfig = {
      "Content-Type": "application/json",
      headers: { "x-api-key": xAPIKey },
    };

    const response = await axios.post(`${baseUrl}/profile/api/v1/forms/target?user_guid=${userId}`, null, reqConfig);
    const isSuccessful = response.data?.success || false;
    log.info(`First target Form Assignment for userId: ${userId}, ${JSON.stringify(response.data)}`);
    return isSuccessful;
  } catch (error) {
    log.warn(JSON.stringify(error));
    return false;
  }
}

async function getAssignedForms(userId, templateId) {
  try {
    const xAPIKey = config.xAPIKey;
    const reqConfig = {
      "Content-Type": "application/json",
      headers: { "x-api-key": xAPIKey },
    };

    const response = await axios.get(`${baseUrl}/profile/api/v1/forms?user_guid=${userId}&templateId=${templateId}`, reqConfig);
    const isSuccessful = response.data?.success || false;
    if(isSuccessful) {
      log.info(`Assigned Forms fetched for userId: ${userId}, ${JSON.stringify(response.data)}`);
      return response.data?.data || [];
    }
    return [];
  } catch (error) {
    log.warn(JSON.stringify(error));
    return [];
  }
}

module.exports = { 
  assignFirstTargetForm,
  getAssignedForms, 
};
