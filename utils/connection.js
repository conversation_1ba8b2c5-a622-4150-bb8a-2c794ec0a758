const { Client, Connection } = require("@opensearch-project/opensearch");
const { defaultProvider } = require("@aws-sdk/credential-provider-node");
const aws4 = require("aws4");
const { sign } = aws4;
const { config } = require("../environment/index");
let OpenSearchClient;

function createAwsConnector(credentials, region) {
  class AmazonConnection extends Connection {
    buildRequestObject(params) {
      const request = super.buildRequestObject(params);
      request.service = "es";
      request.region = region;
      request.headers = request.headers || {};
      request.headers["host"] = request.hostname;

      return sign(request, credentials);
    }
  }
  return {
    Connection: AmazonConnection,
  };
}

async function createClient() {
  const credentials = await defaultProvider()();
  OpenSearchClient = new Client({
    ...createAwsConnector(credentials, config.REGION),
    node: config.OS_HOST,
  });
  return OpenSearchClient;
}

function getClient() {
  return OpenSearchClient;
}

module.exports = {
  getClient,
  createClient,
};
