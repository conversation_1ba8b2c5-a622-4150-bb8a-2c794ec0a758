const axios = require("axios").default;
const { config } = require("../environment/index");
const { log } = require("./logger");
const { notificationDescription } = require("../service/push-notifications");
const { getConnectedUserList } = require("../utils/aws");
const baseUrl = `${config.MONOLITH.SERVER_URL}`;

async function createRequestEntry(requesteeGuid, accessToken, notificationName, detail) {
  try {
    const reqConfig = {
      "Content-Type": "application/json",
      headers: { "X-Access-Token": accessToken },
    };

    const reqBody = getRequestData(requesteeGuid, notificationName, detail);

    const response = await axios.post(`${baseUrl}/api/requests`, reqBody, reqConfig);
    const isSuccessful = response.data?.success || false;
    log.info(`Request created: ${isSuccessful}`);
    return isSuccessful;
  } catch (error) {
    log.warn(JSON.stringify(error));
    return false;
  }
}

async function createRequestEntryToConnectedUsers(userId, accessToken, notificationName, detail) {
  try {
    const connectedUsers = await getConnectedUserList(userId);
    if (!connectedUsers) {
      return false;
    }
    await Promise.all(connectedUsers.map((connectedUserId) =>
        createRequestEntry(connectedUserId, accessToken, notificationName, detail))
    );
    return true;
  } catch (error) {
    log.warn(`Error calling sendNotificationToConnectedBUsers, ${JSON.stringify(error)}`);
    return false;
  }
}

function getRequestData(requesteeGuid, notificationName, detail = { entity_id: null }) {
  const { alert, type } = notificationDescription[notificationName];
  const reqData = {
    requestee_guid: requesteeGuid, // receiver
    type: type,
    message: alert,
    detail: detail,
  };
  return reqData;
}

module.exports = { 
  createRequestEntry, 
  createRequestEntryToConnectedUsers, 
};
