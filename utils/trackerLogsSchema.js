const { roundOffNumber } = require("./helpers");

// Define the precedence order, must be in lower case only
const precedenceOrder = [
  'oura',
  'watch',
  'health'
];

function sortAndFilterLogs(logs) {
  // Filter out only one source
  const uniqueSources = [...new Set(logs.map(log => log.device))];
  if (!uniqueSources.length) return []

  let highestPrecedenceSource;
  for (let precedenceSource of precedenceOrder) {
    let highestPrecedenceSources = uniqueSources.filter(source => source.toLowerCase().includes(precedenceSource));
    if (highestPrecedenceSources.length) {
      highestPrecedenceSource = highestPrecedenceSources[0];
      break;
    }
  }

  if (!highestPrecedenceSource) {
    highestPrecedenceSource = uniqueSources[0]
  }
  // Filter out records with the highest precedence source
  logs = logs.filter(item => (item?.device || "").toLowerCase() == highestPrecedenceSource.toLowerCase());

  // Sort logs by startTime and then by timeInBed
  logs.sort((a, b) => {
    const startTimeComparison = new Date(a.startTime) - new Date(b.startTime);
    if (startTimeComparison === 0) {
      // If start times are the same, compare by timeInBed (highest timeInBed first)
      return b.timeInBed - a.timeInBed;
    }
    return startTimeComparison;
  });

  const filteredLogs = [];
  let currentLog = null;

  for (const log of logs) {
    if (!currentLog) {
      // If no current log with the same start time, start with this log
      currentLog = log;
    } else {
      // Check if the start times are the same
      if (log.startTime === currentLog.startTime) {
        // If the current log has a shorter timeInBed, replace it
        if (log.timeInBed > currentLog.timeInBed) {
          currentLog = log;
        }
      } else {
        // If the start times are different, add the current log to the filtered list
        filteredLogs.push(currentLog);
        currentLog = log;
      }
    }
  }

  // Add the last log to the filtered list
  if (currentLog) {
    filteredLogs.push(currentLog);
  }

  return filteredLogs;
}

function groupSleepRecords(logs) {
  const records = sortAndFilterLogs(logs)

  const groups = [];
  let currentGroup = null;

  for (const record of records) {
    const logEntry = {
      dateTime: record.startTime,
      level: record?.stage || 'rem',
      seconds: record?.duration || 0
    }
    if (!currentGroup) {
      currentGroup = {
        date: record.date,
        timestamp: record.startTime,
        endTime: record.endTime,
        timeInBed: record.duration,
        levels: {
          data: [logEntry],
          summary: {
            deep: { count: 0, seconds: 0, thirtyDayAvgSeconds: 0 },
            light: { count: 0, seconds: 0, thirtyDayAvgSeconds: 0 },
            rem: { count: 0, seconds: 0, thirtyDayAvgSeconds: 0 },
            wake: { count: 0, seconds: 0, thirtyDayAvgSeconds: 0 }
          }
        }
      };
    } else {
      const currentEndTime = new Date(currentGroup.endTime);
      const recordStartTime = new Date(record.startTime);
      const timeDifference = (recordStartTime - currentEndTime) / (1000 * 60);

      if (timeDifference <= 15) {
        currentGroup.endTime = record.endTime;
        currentGroup.timeInBed += record.duration;
        currentGroup.levels.data.push(logEntry);
        currentGroup.levels.data.sort((a, b) => new Date(a.dateTime) - new Date(b.dateTime));
        currentGroup.date = record.date;
      } else {
        groups.push(currentGroup);
        currentGroup = {
          date: record.date,
          timestamp: record.startTime,
          endTime: record.endTime,
          timeInBed: record.duration,
          levels: { 
            data: [logEntry],
            summary: {
              deep: { count: 0, seconds: 0, thirtyDayAvgSeconds: 0 },
              light: { count: 0, seconds: 0, thirtyDayAvgSeconds: 0 },
              rem: { count: 0, seconds: 0, thirtyDayAvgSeconds: 0 },
              wake: { count: 0, seconds: 0, thirtyDayAvgSeconds: 0 }
            }            
          },
        };
      }
    }

    // Update group summary
    const level = logEntry.level
    currentGroup.levels.summary[level].count += 1
    currentGroup.levels.summary[level].seconds += logEntry.seconds
  }

  if (currentGroup) {
    groups.push(currentGroup);
  }

  // Sort records within each group by startTime
  groups.forEach((group) => {
    group.startTime = group.timestamp;
    group.timestamp = group.endTime;
    group.duration = group.timeInBed - (group?.levels?.summary?.wake?.seconds || 0);
    delete group?.date;
    group.levels.data.sort((a, b) => new Date(a.startTime) - new Date(b.startTime));
  });
  return groups;
}

function modifyHRIntraDayTimeStamp(timestamp, intraDayValues){
  intraDayValues = intraDayValues.map(entry => {
    return { timestamp: entry?.timestamp || null, value: roundOffNumber(entry?.value || 0, 0) };
  });
  return intraDayValues;
}

const formatter = {
  // Sleep
  '5': (log) => {
    const { data, timestamp, ...commonProps } = log
    if (!data) return [log]
    const groups = groupSleepRecords(data)
    return groups.map(group => {
      delete commonProps?.logId;
      return {...group, ...commonProps}
    })
  },
  '8': (log) => {
    const { data, ...commonProps } = log;
    if (!data) return [log]
    return data.map(doc => {
      delete commonProps?.logId;
      return {...doc, ...commonProps}
    })
  },
  '10': (log) => {
    if(log.intraday) log.intraday = modifyHRIntraDayTimeStamp(log.timestamp, log.intraday);
    return [log];
  }
}

const supportedTrackerIds = Object.keys(formatter)

function formatLogs(trackerId, log) {
  let id = trackerId.toString()
  if (supportedTrackerIds.includes(id)) {
    return formatter[id](log)
  }
  return [log]
}

module.exports = { formatLogs, supportedTrackerIds };
