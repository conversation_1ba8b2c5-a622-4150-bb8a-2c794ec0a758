const AWS = require("aws-sdk");
const { config } = require("../environment/index");
const { log: logger } = require("./logger");

AWS.config.update({ region: config.REGION });

const s3 = new AWS.S3({ region: config.REGION });
const sqs = new AWS.SQS({ apiVersion: "2012-11-05", region: config.REGION });

const S3_BUCKET = config.AWS.TRACKERS_INGESTION_LOGS_S3_BUCKET;
const SQS_URL = config.AWS.TRACKERS_INGESTION_SQS_URL;

async function addLogsToQueue(userId, data) {
  try {
    const refId = Date.now();
    const key = `users/${userId}/tracker_logs/${refId}.json`;

    console.time(`uploadToS3`);
    await uploadToS3(key, data);
    console.timeEnd(`uploadToS3`);

    console.time(`sendMessageToSQS`);
    await sendMessageToSQS({ userId, s3Key: key });
    console.timeEnd(`sendMessageToSQS`);

    logger.info(`Posted S3 URL to SQS with Path: ${key}`);
    return true;
  } catch (error) {
    logger.error(`Error in addLogsToQueue: ${JSON.stringify(error)}`);
    return false;
  }
}

async function uploadToS3(key, data) {
  const params = {
    Bucket: S3_BUCKET,
    Key: key,
    Body: JSON.stringify(data),
    ContentType: "application/json",
  };
  const resp = await s3.putObject(params).promise();
  logger.info(`Uploaded to S3: ${JSON.stringify(resp)}`);
}

async function sendMessageToSQS(message) {
  const params = {
    MessageAttributes: {},
    MessageBody: JSON.stringify(message),
    QueueUrl: SQS_URL,
    MessageGroupId: "TrackersIngestion",
  };
  const resp = await sqs.sendMessage(params).promise();
  logger.info(`sendMessageToSQS: ${JSON.stringify(resp)}`);
}

module.exports = {
  addLogsToQueue,
};
