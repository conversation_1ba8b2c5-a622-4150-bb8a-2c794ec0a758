const { getClient } = require("../utils/connection");
const { config } = require("../environment/index");
const { log } = require("../utils/logger");
const indexName = config.INDEX.triggers;

async function createTrigger(document) {
  const client = await getClient();
  const response = await client.index({
    index: indexName,
    body: document,
  });
  return response.body?._id;
}

async function getAllTriggers(userId, startTime, endTime) {
  const client = await getClient();
  const response = await client.search({
    index: indexName,
    body: {
      _source: {
        excludes: ["userId"],
      },
      sort : { startedAt: "desc" },
      query: {
        bool: {
          must: [
            { match: { "userId.keyword": userId } },
            {
              range: {
                startedAt: {
                  from: startTime,
                  to: endTime,
                },
              },
            },
          ],
        },
      },
    },
  });
  if (response.body?.hits) {
    const data = response.body?.hits?.hits?.map((itm) => {
      itm._source._id = itm._id;
      return itm._source;
    });
    return data;
  }
  return [];
}

async function getTriggerById(userId, triggerId) {
  const client = await getClient();
  const response = await client.search({
    index: indexName,
    body: {
      _source: { excludes: ["userId"] },
      query: {
        bool: {
          must: [{ match: { _id: triggerId } }, { match: { "userId.keyword": userId } }],
        },
      },
    },
  });

  const data = response.body?.hits?.hits[0]?._source || null;
  return data;
}

async function updateTrigger(triggerId, body) {
  const client = await getClient();
  const updateResponse = await client.update({
    index: indexName,
    id: triggerId,
    body: {
      doc: {
        ...body,
      },
    },
  });

  log.debug({ message: "Updated trigger details", triggerId, body });
  return updateResponse.body;
}

async function getTriggersByTag(userId, startTime, endTime, tagToSearch) {
  tagToSearch = tagToSearch.trim().toLowerCase();
  const client = await getClient();
  const response = await client.search({
    index: indexName,
    body: {
      _source: {
        excludes: ["userId"],
      },
      sort : { startedAt: "desc" },
      query: {
        bool: {
          must: [
            { match: { "userId.keyword": userId } },
            {
              range: {
                startedAt: {
                  from: startTime,
                  to: endTime,
                },
              },
            },
          ],
        },
      },
    },
  });
  if (response.body?.hits) {
    let data = response.body?.hits?.hits?.map((itm) => {
      itm._source._id = itm._id;
      return itm._source;
    });
    data = data.filter(itm => {
      const triggers = itm.triggers;
      for (let trigger of triggers) {
        if(trigger.mealTags.find(mealTag => mealTag.tagName == tagToSearch)) return true;
      }
      return false;
    })
    return data;
  }

  return [];
}

async function getActiveTrigger(userId) {
  const client = await getClient();
  const currentTime = new Date().toISOString();

  const response = await client.search({
    index: indexName,
    body: {
      _source: { excludes: ["userId"] },
      sort : { expiresAt: "desc" },
      query: {
        bool: {
          must: [
            { match: { "userId.keyword": userId } },
            { range: { expiresAt: { gte: currentTime } } },
          ],
        },
      },
      size: 1
    },
  });
  const data = response.body?.hits?.hits[0] || null;
  return data ? { _id: data._id, ...data._source } : null;
}

const triggerService = {
  createTrigger,
  getAllTriggers,
  getTriggerById,
  updateTrigger,
  getTriggersByTag,
  getActiveTrigger,
};

module.exports = {
  triggerService,
};
