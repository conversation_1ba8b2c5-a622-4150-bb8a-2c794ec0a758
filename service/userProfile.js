const { getClient } = require("../utils/connection");
const { config } = require("../environment/index");

// hardcoded for IST, incase value not found in DB
const UTCOffsetMin = 330;

const indexName = config.INDEX.userProfiles;

async function getUTCOffsetValue(userId) {
  const client = getClient();
  const response = await client.search({
    index: indexName,
    _source: ["UTCOffsetMin"],
    body: {query: {bool: {must: [{ match: { "userId.keyword": userId } }]}}, size: 1},
  });

  const data = response.body?.hits?.hits[0]?._source || null;
  return data?.UTCOffsetMin || UTCOffsetMin;
}

async function upsertUTCOffsetValue(userId, UTCOffsetMin) {
  try {
    if(UTCOffsetMin === undefined || UTCOffsetMin === null) {
      return true;
    }
    const client = getClient();
    const { body: { hits } } = await client.search({
      index: indexName,
      body: { query: { match: { "userId.keyword": userId } }, size: 1 },
    });
    let response;
    if (hits?.hits?.length) {
      response = await client.update({
        index: indexName,
        id: hits.hits[0]._id,
        body: { doc: { UTCOffsetMin } },
      });
    } else {
      response = await client.index({
        index: indexName,
        body: { userId, UTCOffsetMin },
      });
    }

    return true;
  } catch (error) {
    console.error("Error in upserting UTCOffsetMin:", JSON.stringify(error));
    return false;
  }
}

module.exports = {
  getUTCOffsetValue,
  upsertUTCOffsetValue,
}
