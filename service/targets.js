const { getClient } = require("../utils/connection");
const utils = require("./utils.js");
const { config } = require("../environment/index");
const { getStaticTargetsMap } = require("../utils/staticData");
const targetCronService = require("../service/targetCron");

const indexName = config.INDEX.targets

async function insertNewTarget(document) {
  //Inserting in targetCron schema to calculate active window
  await targetCronService.insertData(document);
  return await utils.insertData(indexName, document)
}

async function getLatestTargetById(userId, targetId) {
  const client = getClient();
  const response = await client.search({
    index: indexName,
    body: {
      _source: { excludes: ["userId"] },
      sort: [{ "createdAt": { order: "desc" } }],
      query: {
        bool: {
          must: [{ match: { targetId } }, { match: { "userId.keyword": userId } }],
        },
      },
      size: 1,
    },
  });

  return response.body?.hits?.hits[0]?._source || null
}

async function getLatestTargetsByIds(userId, targetIds) {
  const client = getClient();
  const { body } = await client.search({
    index: indexName,
    body: {
      size: 0,
      query: { bool: { must: [{ terms: { targetId: targetIds } }, { match: { "userId.keyword": userId } }] } },
      aggs: {
        latest_per_target: {
          terms: { field: "targetId", size: targetIds.length },
          aggs: {
            latest_doc: { top_hits: { _source: { excludes: ["userId"] }, size: 1, sort: [{ createdAt: "desc" }] } },
          },
        },
      },
    },
  });

  return body.aggregations.latest_per_target.buckets.map(b => ({
    targetId: b.key,
    ...b.latest_doc.hits.hits[0]._source
  }));
}

/**
 * Returns latest targets set for that date for each targetId in targetIds Array
 * e.g.
 * If date passed is "2023-10-31" then it will return latest doc createdAt < "2023-11-01" for each targetId
 * */
async function getLatestTargetsByDate(userId, targetIds, date) {
  if (!Array.isArray(targetIds) || targetIds.length === 0) {
    return [];
  }

  const client = getClient();
  const response = await client.search({
    index: indexName,
    body: {
      _source: { excludes: ["userId"] },
      size: 0, // Set size to 0 to retrieve only aggregations
      aggs: {
        latestDocs: {
          terms: { field: "targetId", size: targetIds.length },
          aggs: {
            latestDoc: {
              top_hits: {
                size: 1,
                sort: [{ "createdAt": { order: "desc" } }],
                _source: { excludes: ["userId"] },
              },
            },
          },
        },
      },
      query: {
        bool: {
          must: [
            { terms: { targetId: targetIds } },
            { match: { "userId.keyword": userId } },
            { range: { createdAt: { lte: date + "T23:59:59.999Z" } } },
          ],
        },
      },
    },
  });

  if (response.body?.aggregations?.latestDocs?.buckets) {
    const data = response.body.aggregations.latestDocs.buckets.map((bucket) => {
      const latestDoc = bucket.latestDoc.hits.hits[0];
      return { id: latestDoc._id, ...latestDoc._source };
    });
    return data;
  }
  return [];
}

async function getTargetsByDateRange(userId, startDate, endDate, targetIds) {
  const client = getClient();

  const query = {
    sort: [{ createdAt: { order: "desc" } }],
    query: {
      bool: {
        must: [
          { match: { "userId.keyword": userId } },
          { range: { createdAt: { from: startDate, to: endDate } } },
        ],
      },
    },
  };

  // Optional filtering based on targetIds
  if (targetIds && targetIds.length > 0) {
    query.query.bool.must.push({ terms: { targetId: targetIds } });
  }

  const response = await client.search({
    index: indexName,
    body: query,
  });

  if (response.body?.hits) {
    const data = response.body?.hits?.hits?.map((itm) => {
      return { id: itm._id, ...itm._source };
    });
    return data;
  }
  return [];
}

async function getStaticTargetsByDuration(duration = 7) {
  const targetsMap = await getStaticTargetsMap();
  const targetsData = Object.fromEntries(
    Object.entries(targetsMap).filter(([key, target]) => target.duration === duration)
  );
  return targetsData;
}

module.exports = {
  insertNewTarget,
  getLatestTargetById,
  getLatestTargetsByIds,
  getLatestTargetsByDate,
  getTargetsByDateRange,
  getStaticTargetsByDuration,
}
