const { getClient } = require("../utils/connection");
const { config } = require("../environment/index");
const { log: logger } = require("../utils/logger");
const utils = require("./utils.js");

const indexName = config.INDEX.wellness_score;

async function insertUser(document) {
  return await utils.insertData(indexName, document)
}

async function upsertWellnessScore(userId, doc) {
  const client = getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        bool: {
          must: [{ match: { "userId.keyword": userId } } ],
        },
      },
    },
  });
  const id = response.body?.hits?.hits[0]?._id || null;
  if (!id) {
    return await insertUser({ ...doc, userId });
  }
  delete doc?.createdAt;
  doc.updatedAt = new Date().toISOString();
  const updateResponse = await client.update({
    index: indexName,
    id,
    body: {
      doc: {
        ...doc,
      },
    },
  });
  logger.debug({ message: "Updated user wellness_score", userId, body: doc });
  return updateResponse.body;
}

module.exports = {
  upsertWellnessScore,
};
