const { getClient } = require("../utils/connection");
const { config } = require("../environment/index");
const { log: logger } = require("../utils/logger");
const utils = require("./utils.js");

const indexName = config.INDEX.recommendations;

async function getUserRecommendations(userId, status, ignoreTypes) {
  const currentTime = new Date().toISOString();
  const client = getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        bool: {
          must: [
            { match: { "userId.keyword": userId } },
            { match: { status } },
            { range: { ttl: { gte: currentTime } } },
          ],
          must_not: [{ terms: { "type.keyword": ignoreTypes }} ]
        },
      },
    },
  });

  if (response.body?.hits) {
    const data = response.body?.hits?.hits?.map((itm) => {
      return { id: itm._id, ...itm._source };
    });
    return data;
  }
  return [];
}

async function insertUser(document) {
  return await utils.insertData(indexName, document)
}

async function upsertUserRecommendations(userId, doc, type) {
  const client = getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        bool: {
          must: [{ match: { "userId.keyword": userId } }, { match: { type } }],
        },
      },
    },
  });
  const id = response.body?.hits?.hits[0]?._id || null;
  if (!id) {
    return await insertUser({ ...doc, userId });
  }
  delete doc?.createdAt;
  doc.updatedAt = new Date().toISOString();
  const updateResponse = await client.update({
    index: indexName,
    id,
    body: {
      doc: {
        ...doc,
      },
    },
  });
  logger.debug({ message: "Updated user recommendations", userId, body: doc });
  return updateResponse.body;
}

module.exports = {
  getUserRecommendations,
  upsertUserRecommendations,
};
