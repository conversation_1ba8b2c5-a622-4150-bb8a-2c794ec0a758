const { getClient, createClient } = require("../utils/connection");
const { config } = require("../environment/index");
const { log } = require("../utils/logger");

indexes = Object.keys(config.INDEX).map(k => config.INDEX[k])

async function createIndex(indexName) {
    await createClient();
    const client = getClient();
    log.info("Creating index: ", indexName);
    const response = await client.indices.create({
      index: indexName,
    });
    log.info(response.body);
}

async function deleteIndex(indexName) {
  await createClient();
  const client = getClient();
  log.info("Deleting index:", indexName);
  const response = await client.indices.delete({
    index: indexName,
  });

  log.info(response.body);
}

async function clearIndex(indexName) {
    await createClient();
    const client = getClient();
  
    const response = await client.search({
      index: indexName,
      body: {size: 10000},
    });
  
    console.log("Found matches");
    console.log(response.body.hits?.hits?.length);
  
    await Promise.all(
      response.body?.hits?.hits?.map(async (itm) => {
        await client.delete({
          index: indexName,
          id: itm._id,
        });
      })
    );
}

async function insertData(indexName, document) {
  // await createClient();
  const client = getClient();
  try {
    const response = await client.index({
      index: indexName,
      body: document,
    });
    return response.body?._id;
  } catch (error) {
    log.warn('Failed to insert Log', JSON.stringify(error));
  }
}

async function upsertData(indexName, document) {
  await createClient();
  const client = getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        bool: {
          must: [
            { match: { "userId.keyword": document.userId } },
            { match: { sourceId: document.sourceId } }
          ],
        },
      },
    },      
  });
  
  const id = response.body?.hits?.hits[0]?._id || null;

  if (id) {
    const updatedLog = await client.update({
      index: indexName, id,
      body: { doc: { ...document } }
    });
    console.log({ message: "Updated activity summary log", document });
    return updatedLog;
  } else {
    console.log("No match")
  }
}

async function getData(indexName) {
  await createClient();
  const client = getClient();
  const response = await client.search({
    index: indexName,
    body: {size: 10000},
  });

  const data = response.body.hits?.hits[0]?._source;
  console.log(JSON.stringify(data));
}

async function deleteLogs(indexName, userId) {
  await createClient();
  const client = getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        bool: {
          must: [
            { match: { "userId.keyword": userId } },
            { match: { sourceId: 2 } },
            // {range: {timestamp: { from: startTime, to: endTime }}},
          ],
        },
      },
      size: 1
    },
  });
  console.log(response.body?.hits?.hits)
  await client.delete({
    index: indexName,
    id: response.body?.hits?.hits[0]?._id,
  });
}

// async function test() {
//   await deleteIndex('vo2')
//   await createIndex('vo2')
// }
// test()

// indexes.forEach(async (element) => {
//   try {
//     console.log(element)
//     await getData(element)
//   } catch (e) { 
//     console.log(e)
//     console.log("Creating new Table: ", element) 
//     await createIndex(element)
//   }
// });

// upsertData('user', {
//     "state": "394b288a-b2a0-4a31-a0a0-2cc3f2119e20",
//     "source": "Fitbit",
//     "sourceId": 1,
//     "trackers": [
//         "weight",
//         "nutrition",
//         "activity",
//         "sleep",
//         "heartrate"
//     ],
//     "createdAt": "2023-02-09T08:05:25.174Z",
//     "updatedAt": "2023-02-09T08:05:25.174Z",
//     "lastSynced": "2023-02-08T08:58:32.928Z",
//     "userId": "8fd8f465-166e-459b-9365-fc8ed3e27f8c",
//     "sourceUserId": "B3CM8P",
//     "accessToken": "eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIyMzhHWkciLCJzdWIiOiJCM0NNOFAiLCJpc3MiOiJGaXRiaXQiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJzY29wZXMiOiJ3aHIgd251dCB3cHJvIHdzbGUgd3dlaSB3c29jIHdhY3Qgd3NldCB3bG9jIiwiZXhwIjoxNjc1OTU4NzQxLCJpYXQiOjE2NzU5Mjk5NDF9.TehtYz1SvEO97WDO59bCFq-yMybbWeHYKLhFIRWyInU",
//     "subscriptionId": "7af6103c-d8dd-4fdf-ad20-f8806a3f3fd6",
//     "expiresAt": "2023-02-09T16:04:41.222Z",
//     "refreshToken": "d7b3f68af8b2b7230daae9a9a7189ebb12be296c40f6cc9af3f13c65166748c7",
//     "offsetFromUTCMillis": 19800000
// })


module.exports = { 
  insertData 
};