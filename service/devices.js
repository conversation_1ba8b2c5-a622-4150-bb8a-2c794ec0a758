const { getClient } = require("../utils/connection");
const { config } = require("../environment/index");
const { log } = require("../utils/logger");
const utils = require("./utils.js");
const { trackersService } = require("./trackers");
const notificationService = require("../service/push-notifications");
const { getStaticTargetsMap, getStaticSourcesArray, getStaticDevices, getStaticSourcesDevicesMapping } = require("../utils/staticData");
const { getAllTargets } = require("../service/targets_achieved");
const { insertNewTarget, getLatestTargetById } = require("./targets");
const { updateIsDeviceConnected } = require("../service/monolithAPIs");
const { assignFirstTargetForm } = require("../utils/forms");
const IHEALTH_DEVICE_ID = 4;
const MANUAL_ENTRY_DEVICE_ID = -1;

const { createRequestEntryToConnectedUsers } = require("../utils/requests");
const notificationEventNames = notificationService.notificationEventNames;

const indexName = config.INDEX.devices;

async function insertData(document) {
  return await utils.insertData(indexName, document)
}

async function getConnectedDevices(userId) {
  const client = await getClient();

  const response = await client.search({
    index: indexName,
    body: {
      query: { match: { "userId.keyword": userId } },
    },
  });

  const data = response.body?.hits?.hits[0]?._source || [];
  return data;
}

async function deleteConnectedDevice(userId, deviceIds) {
  const client = await getClient();
  
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        match: { "userId": userId }
      }
    }
  });

  if (response.body.hits.total.value === 0) {
    return null;
  }

  const docId = response.body.hits.hits[0]._id;
  const userDoc = response.body.hits.hits[0]._source;

  if (!userDoc || !userDoc.connectedDevices) {
    return null;
  }

  // Filter out devices with IDs that match any in the deviceIds array
  userDoc.connectedDevices = userDoc.connectedDevices.filter(device => !deviceIds.includes((device.id).toString()));

  await client.update({
    index: indexName,
    id: docId,
    body: {
      doc: {
        connectedDevices: userDoc.connectedDevices
      }
    }
  });

  log.info(`Device with id ${deviceIds} successfully deleted from user ${userId}`);
}

function mergeTrackerEnableStatus(array1, array2) {
  // Create a map for easy lookup of status based on id
  let isEnabledMap = {};
  array1.forEach(item => {
      isEnabledMap[item.trackerId] = item.isEnabled;
  });

  // Update array1 based on array2
  array2.forEach(item => {
      if (isEnabledMap[item.trackerId] !== undefined) {
          isEnabledMap[item.trackerId] = item.isEnabled;
      }
  });

  // Convert isEnabledMap back to array
  return Object.keys(isEnabledMap).map(trackerId => ({ 
    trackerId: parseInt(trackerId), 
    isEnabled: isEnabledMap[trackerId] 
  }));
}

async function connectNewDevices(userId, document, replaceExisting = true, accessToken) {
  const client = await getClient()
  try{
    const response = await client.search({
      index: indexName,
      body: {
        query: { match: { "userId.keyword": userId } },
      },
    });
    let id = response.body?.hits?.hits[0]?._id || null;
    let newDevice = document.newDevice;
    delete document.newDevice;

    const supportedTrackers = await devicesService.getSupportingTrackersByDeviceId(newDevice.id);
    newDevice.serialNumber = Array.from(new Set(newDevice?.serialNumber || []));

    if (id) {
      // User has already connected some devices
      const data = response.body?.hits?.hits[0]?._source || {};
      let isNewDevicePresentAlready = false

      const connectedDevices = data?.connectedDevices?.map(cdv => {
        if (cdv.id === newDevice.id) {
          isNewDevicePresentAlready = true;
          const trackers = mergeTrackerEnableStatus(supportedTrackers, cdv?.trackers || [])
          newDevice.trackers = mergeTrackerEnableStatus(trackers, newDevice?.trackers || [])
          newDevice.serialNumber = Array.from(new Set(
            [...(cdv?.serialNumber || []), ...(newDevice?.serialNumber || [])]
          ));
          // Disconnecting device if all the supported Trackers are disabled
          const allTrackersDisabled = newDevice.trackers.every(tr => tr.isEnabled == false)
          if (allTrackersDisabled || !newDevice.isConnected) {
            newDevice.isConnected = false; newDevice.serialNumber = [];
          }
          return newDevice
        }
        return cdv
      }) || [];
      if (!isNewDevicePresentAlready) {
        newDevice.trackers = mergeTrackerEnableStatus(supportedTrackers, newDevice?.trackers || [])
        // Disconnecting device if all the supported Trackers are disabled
        const allTrackersDisabled = newDevice.trackers.every(tr => tr.isEnabled == false)
        if (allTrackersDisabled || !newDevice.isConnected) {
          newDevice.isConnected = false; newDevice.serialNumber = [];
        }
        connectedDevices.push(newDevice)
      }

      // If device already present, don't auto update for new logs
      if (isNewDevicePresentAlready && !replaceExisting) return;

      document.connectedDevices = connectedDevices;
      delete document?.createdAt;
      await client.update({
        index: indexName, id,
        body: { doc: { ...document } }
      });
      log.debug({ message: "Updated Devices", document });
    } else {
      newDevice.trackers = mergeTrackerEnableStatus(supportedTrackers, newDevice?.trackers || [])
      // Disconnecting device if all the supported Trackers are disabled
      const allTrackersDisabled = newDevice.trackers.every(tr => tr.isEnabled == false)
      if (allTrackersDisabled || !newDevice.isConnected) {
        newDevice.isConnected = false; newDevice.serialNumber = [];
      }
      document.connectedDevices = [newDevice];
      id = await insertData(document);
      const isNotificationSent = await notificationService.sendNotificationToConnectedUsers(userId, notificationEventNames.newDeviceConnected);
      log.info(`isNotificationSent, ${isNotificationSent}`);
      if(isNotificationSent) {
        await createRequestEntryToConnectedUsers(userId, accessToken, notificationEventNames.newDeviceConnected, { entity_id: null });
      }
    }
    
    // Update Default device
    if (newDevice?.isConnected) {
      const defaultDevices = await trackersService.getAllDefaultTrackers(userId)
      let updatedDefaultDevices = []
      newDevice?.trackers?.forEach(tr => {
        if (!tr?.isEnabled) return
        let notMatched = true
        if (defaultDevices?.defaultDevices) {
          notMatched = defaultDevices?.defaultDevices.every(dd => dd.trackerId != tr.trackerId)
        }
        if (notMatched) {
          updatedDefaultDevices.push({
            trackerId: Number(tr.trackerId), deviceId: Number(newDevice.id)
          })
        }
      });
  
      await trackersService.updateDefaultTrackers(userId, {
        userId, ...defaultDevices, defaultDevices: updatedDefaultDevices
      })

      // User won't be setting targets for few trackers himself, so need to set targets forcefully
      const supportedTrackerIds = supportedTrackers.filter((tracker) => tracker.isEnabled === true).map((tracker) => tracker.trackerId);
      const targetsMap = await getStaticTargetsMap();
      const insertedTargetIds = [];
      const forceSettingTargetsTrackerIds = [10, 13, 18];
      for (const trackerId of forceSettingTargetsTrackerIds) {
        if (supportedTrackerIds.includes(trackerId)) {
          const targetIds = getAllTargets(trackerId, targetsMap);
          const createdAt = new Date().toISOString();
          for (const targetId of targetIds) {
            const latestTarget = await getLatestTargetById(userId, targetId);
            if (!latestTarget && !latestTarget?.isActive) {
              const targetDoc = { userId, createdAt, isActive: true, targetId: Number(targetId), value: 0 };
              const id = await insertNewTarget(targetDoc);
              if (id) insertedTargetIds.push(targetId);
            }
          }
        }
      }
      log.info(`Forcefully setting targetIds: ${insertedTargetIds}`);

      // Assign first target form if conditions met
      const targetFormAssignmentResponse = await assignFirstTargetForm(userId);
    }
    else {
      const deviceId = newDevice.id.toString();
      await devicesService.deleteConnectedDevice(userId, [deviceId]);
    }

    // isFitnessEnabled flag - Here, you need to check if any device is connected except iHealth devices
    const connectedDevices = document?.connectedDevices || [];
    const iHealthDeviceIds = await devicesService.getDeviceIdsBySourceId(IHEALTH_DEVICE_ID);
    const isDeviceConnected = connectedDevices.some(device => device?.isConnected && !iHealthDeviceIds.includes(Number(device.id)));
    log.info(`Updating isDeviceConnected ${isDeviceConnected} for userId: ${userId}`);
    await updateIsDeviceConnected(userId, isDeviceConnected); 

    return {id, ...document};
  } catch (error) {
    log.warn("Failed to upsert Devices", error);
  }
}

/**
 * sample o/p = {trackerId: [array of sourceIds]}
 * { "5": [ -1, 1, 3, 5] }
 */
async function getSupportingSources(trackerIds) {
  const supportingSources = {};
  const sources = await getStaticSourcesArray();
  for (const trackerId of trackerIds) {
    supportingSources[trackerId] = [];
    for (const source of sources) {
      if (source.trackers.includes(trackerId)) {
        supportingSources[trackerId].push(source.id);
      }
    }
  }

  return supportingSources;
}

/**
 * sample o/p = {trackerId: [array of deviceIds]}
 * { "5": [ -1, 11, 31, 51] }
 */
async function getSupportingDevices(supportingSources) {
  const supportingDevices = {};
  const sourcesDevicesMapping = await getStaticSourcesDevicesMapping();
  for (const trackerId of Object.keys(supportingSources)) {
    supportingDevices[trackerId] = [];
    for (const sourceId of supportingSources[trackerId]) { 
      supportingDevices[trackerId] = supportingDevices[trackerId].concat(sourcesDevicesMapping[sourceId]);
    }
  }

  return supportingDevices;
}

/**
 * Connected devices (isConnected: true) with given trackerIds as isEnabled: true
 * { "14": [31], "19": [31] }
 */
function getConnectedDeviceIds(connectedDevices = [], trackerIds) {
  const connectedDeviceIds = {};

  for (const item of connectedDevices) {
    const { trackers, id } = item;

    for (const tracker of trackers) {
      if (trackerIds.includes(tracker.trackerId) && item.isConnected) {
        if (!connectedDeviceIds[tracker.trackerId]) {
          connectedDeviceIds[tracker.trackerId] = { enabled: [], disabled: [] };
        }
        if (tracker.isEnabled) {
          connectedDeviceIds[tracker.trackerId].enabled.push(id);
        } else {
          connectedDeviceIds[tracker.trackerId].disabled.push(id);
        }
      }
    }
  }

  return connectedDeviceIds;
}

async function isEnableTargetForUser(userId, targetId) {
  const alwaysAllowTargetIds = [10, 11, 15, 18, 19, 20, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39];
  if(alwaysAllowTargetIds.includes(Number(targetId))){
    return { isEnable: true };
  }
  const targetsMap = await getStaticTargetsMap();
  const trackerIds = targetsMap[targetId].trackerIds;
  return isEnableTrackerForUser(userId, trackerIds);
}

// Checks if any device is connected having isEnabled: true which supports all given trackerIds
async function isEnableTrackerForUser(userId, trackerIdsToCheck) {
  const { connectedDevices } = await getConnectedDevices(userId);
  const supportingSources = await getSupportingSources(trackerIdsToCheck);
  const supportingDevices = await getSupportingDevices(supportingSources);
  const connectedDeviceIds = getConnectedDeviceIds(connectedDevices, trackerIdsToCheck);
  let type, disabledTrackerIds = [], disconnectedDeviceId;
  //Checking if atleast one connected device exists in supporting devices for each tracker
  for (const trackerId in supportingDevices) {
    const supportingDeviceIds = supportingDevices[trackerId];
    // if a tracker is supported by only manual entry, then device condition should not be checked
    const isOnlyManualDeviceSupporting = (supportingDeviceIds.length == 1 && supportingDeviceIds[0] == MANUAL_ENTRY_DEVICE_ID)
    if(!isOnlyManualDeviceSupporting) {
      if (!connectedDeviceIds[trackerId] || (connectedDeviceIds[trackerId]?.enabled.length === 0 && connectedDeviceIds[trackerId]?.disabled.length === 0)) {
        if(!type) type = 'device';
        disabledTrackerIds.push(trackerId);
      }
      else if ((connectedDeviceIds[trackerId]?.enabled.length === 0 && connectedDeviceIds[trackerId]?.disabled.length > 0)) {
        const defaultDeviceId = await devicesService.getDefaultDeviceIdByTrackerId(userId, trackerId);
        const deviceDetails = await getDeviceDetailsByDeviceId(defaultDeviceId ? defaultDeviceId : connectedDeviceIds[trackerId]?.disabled[0]);
        if(!type) type = 'permission';
        disabledTrackerIds.push(trackerId);
        disconnectedDeviceId = deviceDetails.id;
      }
      const intersection = supportingDeviceIds.filter((id) => connectedDeviceIds[trackerId]?.enabled.includes(id));
      if (intersection.length === 0 && connectedDeviceIds[trackerId]?.disabled.length > 0) {
        const defaultDeviceId = await devicesService.getDefaultDeviceIdByTrackerId(userId, trackerId);
        const deviceDetails = await getDeviceDetailsByDeviceId(defaultDeviceId ? defaultDeviceId : connectedDeviceIds[trackerId]?.disabled[0]);
        if(!type) type = 'device';
        disabledTrackerIds.push(trackerId);
        disconnectedDeviceId = deviceDetails.id;
      }
    }
  }
  if(disabledTrackerIds.length > 0) {
    return { isEnable: false, type, trackerIds: disabledTrackerIds, deviceId: disconnectedDeviceId || null };
  }
  return { isEnable: true, type: null, trackerIds: [], deviceId: null };
}

async function getSupportingTrackersByDeviceId(deviceId) {
  const devices = await getStaticDevices();
  const supportedTrackers = devices.find(device => device.id == deviceId)?.trackers || [];
  return supportedTrackers;
}

async function getDefaultDeviceIdBySource(sourceId) {
  const sourcesDevicesMapping = await getStaticSourcesDevicesMapping();
  const deviceId = sourcesDevicesMapping?.[sourceId]?.[0] || null;
  return deviceId;
}

async function getDeviceIdsBySourceId(sourceId) {
  const sourcesDevicesMapping = await getStaticSourcesDevicesMapping();
  const deviceIds = sourcesDevicesMapping?.[sourceId] || [];
  return deviceIds;
}

async function getDefaultDeviceIdByTrackerId(userId, trackerId) {
  let resp = await trackersService.getAllDefaultTrackers(userId);
  let defaultDevice = resp?.defaultDevices?.filter(x => x.trackerId == trackerId)?.[0]

  if (defaultDevice) {
    return defaultDevice?.deviceId;
  }
  return null;
}

async function getDeviceDetailsByDeviceId(deviceId) {
  const staticDeviceData = await getStaticDevices();
  const deviceDetails = staticDeviceData.find(doc => doc.id == deviceId);
  return deviceDetails;
}

const devicesService = {
  getConnectedDevices,
  connectNewDevices,
  getSupportingTrackersByDeviceId,
  isEnableTargetForUser,
  isEnableTrackerForUser,
  getDefaultDeviceIdBySource,
  getDeviceIdsBySourceId,
  deleteConnectedDevice,
  getDefaultDeviceIdByTrackerId,
  getDeviceDetailsByDeviceId,
};

module.exports = {
  devicesService,
};
