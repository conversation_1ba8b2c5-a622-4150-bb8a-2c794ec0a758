const { getClient } = require("../utils/connection");
const { config } = require("../environment/index");
const { log } = require("../utils/logger");
const utils = require("./utils.js");
const { getConnectedUserList }  = require("../utils/aws");

const indexName = config.INDEX.user;
const MANUAL_ENTRY_DEVICE_ID = -1;

async function insertUser(document) {
  return await utils.insertData(indexName, document)
}

async function getUserDetails(userId, sourceId) {
  const client = getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        bool: {
          must: [{ match: { "userId.keyword": userId } }, { match: { sourceId } }],
        },
      },
    },
  });

  const data = response.body?.hits?.hits[0]?._source || null;
  return data;
}

async function getUserDetailsBySourceIds(userId, sourceIds) {
  const client = getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        bool: {
          must: [
            { match: { "userId.keyword": userId } },
            { terms: { sourceId: sourceIds } }, // multiple values
          ],
        },
      },
    },
  });

  const data = response.body?.hits?.hits || [];
  return data.map(hit => hit._source);
}

async function getUserByState(state, sourceId) {
  const client = getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        bool: {
          must: [{ match: { state } }, { match: { sourceId } }],
        },
      },
    },
  });

  const data = response.body?.hits?.hits[0]?._source || null;
  return data;
}

async function getUserBySubscriptionId(subscriptionId) {
  const client = getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        match: { subscriptionId },
      },
    },
  });

  const data = response.body?.hits?.hits[0]?._source || null;
  return data;
}

async function upsertUserDetails(userId, body, sourceId) {
  const client = getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        bool: {
          must: [{ match: { "userId.keyword": userId } }, { match: { sourceId } }],
        },
      },
    },
  });
  const id = response.body?.hits?.hits[0]?._id || null;
  if (!id) {
    return await insertUser({ ...body, userId });
  }
  delete body?.createdAt;
  body.updatedAt = new Date().toISOString();
  const updateResponse = await client.update({
    index: indexName,
    id,
    body: {
      doc: {
        ...body,
      },
    },
  });

  log.debug({ message: "Updated user details", userId, body });
  return updateResponse.body;
}

async function getAllUsers(sourceId) {
  const client = getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        match: { sourceId }
      }
    },
    size: 10000
  });

  if (response.body?.hits) {
    const data = response.body?.hits?.hits?.map((itm) => {
      return itm._source;
    });
    return data;
  }
  return [];
}

async function getUsersIfCaptured(connectedUserList, trackerId, trackerName, startDate, endDate, isLogCaptured, defaultDevicesPerUser) {
  let filterParam = 'timestamp';
  let capturedUsers = [], uncapturedUsers = [];
  const client = getClient();
  await Promise.all([...connectedUserList].map(async (userId) => {
    let query = {
      bool: {
        must: [
          { match: { "userId.keyword": userId } },
          { range: { [filterParam]: { from: startDate, to: endDate } } },
        ],
        should: [
          { match: { deviceId: MANUAL_ENTRY_DEVICE_ID } }
        ], 
        minimum_should_match: 1,
      },
    };
    const defaultDevices = defaultDevicesPerUser[userId];
    const defaultDevice = defaultDevices?.filter(x => x.trackerId == trackerId)?.[0]

    if (defaultDevice && defaultDevice?.deviceId) {
      query.bool.should.push({ match: { deviceId: defaultDevice.deviceId } });
    }
    response = await client.search({
      index: trackerName,
      body: {
        _source: { includes: [filterParam] },
        sort: [{ [filterParam]: { order: "desc" } }],
        size: 1,
        query,
      },
    });
    const trackerLog = response.body?.hits?.hits || [];
    if (trackerLog.length) {
      capturedUsers.push({ userId: userId, lastCaptured: trackerLog[0]?._source?.date,});
    } else {
      uncapturedUsers.push({ userId: userId, lastCaptured: null });
    }
  }));
    
  return isLogCaptured ? capturedUsers : uncapturedUsers;
}

async function deleteUser(userId, sourceId) {
  const client = getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        bool: {
          must: [{ match: { "userId.keyword": userId } }, { match: { sourceId } }],
        },
      },
    },
  });
  const doc = response.body?.hits?.hits[0]?._source || null;
  const id = response.body?.hits?.hits[0]?._id || null;
  if (id && doc) {
    await client.delete({
      index: indexName,
      id,
    });
    // Return deleted document
    return doc;
  }
  return null;
}

const userService = {
  insertUser,
  getUserDetails,
  getUserDetailsBySourceIds,
  getUserByState,
  getUserBySubscriptionId,
  upsertUserDetails,
  getAllUsers,
  deleteUser,
  getUsersIfCaptured
};

module.exports = { userService };
