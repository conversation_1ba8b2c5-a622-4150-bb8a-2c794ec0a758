const { getClient } = require("../utils/connection");
const { config } = require("../environment/index");
const indexName = config.INDEX.targets_achieved

async function getTargetAchievedByIdAndDateRange(userId, targetId, startDate, endDate, fetchNullTarget = false) {
  const client = getClient();
  let query = {
    sort: [{ date: { order: "desc" } }],
    query: {
      bool: {
        must: [
          { match: { "userId.keyword": userId } },
          { match: { targetId } },
          { range: { date: { from: startDate, to: endDate } } },
        ],
      },
    },
  };

  if (!fetchNullTarget) {
    if(Number(targetId) != 19)
      query.query.bool.must.push({ exists: { field: "target" } });
    else {
      query.query.bool.must.push({ exists: { field: "targetDiastole" } });
      query.query.bool.must.push({ exists: { field: "targetSystole" } });
    }
  }

  const response = await client.search({
    index: indexName,
    body: query,
  });

  if (response.body?.hits) {
    const data = response.body?.hits?.hits?.map((itm) => {
      return { id: itm._id, ...itm._source }
    });
    return data;
  }
  return [];
}

async function getTargetAchievedByIdAndPagination(userId, from, size, targetId, fetchNullTarget = false) {
  const client = getClient();
  let query = {
    sort: [{ date: { order: "desc" } }],
    query: {
      bool: {
        must: [
          { match: { "userId.keyword": userId } },
          { match: { targetId } },
        ],
      },
    },
    from, 
    size,
  };

  if (!fetchNullTarget) {
    if(Number(targetId) != 19)
      query.query.bool.must.push({ exists: { field: "target" } });
    else {
      query.query.bool.must.push({ exists: { field: "targetDiastole" } });
      query.query.bool.must.push({ exists: { field: "targetSystole" } });
    }
  }

  const response = await client.search({
    index: indexName,
    body: query,
  });

  if (response.body?.hits) {
    const data = response.body?.hits?.hits?.map((itm) => {
      return { id: itm._id, ...itm._source }
    });
    return data;
  }
  return [];
}

async function getTargetsAchievedByDateRange(userId, startDate, endDate, targetIds, fetchNullTarget = false) {
  const client = getClient();
  let query = {
    sort: [{ date: { order: "desc" } }],
    size: 5000,
    query: {
      bool: {
        must: [
          { match: { "userId.keyword": userId } },
          { range: { date: { from: startDate, to: endDate } } },
        ],
      },
    },
  };

  targetIds = targetIds?.filter(x => x !== '');
  query.query.bool.filter = {
    terms: { targetId: targetIds }
  };

  if (!fetchNullTarget) {
    query.query.bool.must.push({ exists: { field: "target" } });
  }
  // BP target data, need to call explicitly, as keys are different
  let BPData = [];
  if(targetIds.includes(19) || targetIds.includes('19'))
    BPData = await getTargetAchievedByIdAndDateRange(userId, 19, startDate, endDate, fetchNullTarget);

  const response = await client.search({
    index: indexName,
    body: query,
  });
  
  if (!response.body?.hits) {
    return BPData;
  } else if (response.body?.hits) {
    const data = response.body?.hits?.hits?.map((itm) => {
      return { id: itm._id, ...itm._source };
    });
    return BPData.length > 0 ? [...BPData, ...data] : data;
  }
  return [];
}

async function getTargetsAchievedByPagination(userId, from, size, targetIds, fetchNullTarget = false) {
  const client = getClient(); 
  const results = await Promise.all(targetIds.map(async (targetId) => {
    let query = {
      query: {
        bool: {
          must: [
            { match: { "userId.keyword": userId } }, 
            { match: { targetId } }
          ],
        }
      },
      size,
      from,
      sort: [{ date: "desc" }]
    };

    if (!fetchNullTarget) {
      if(Number(targetId) != 19)
        query.query.bool.must.push({ exists: { field: "target" } });
      else {
        query.query.bool.must.push({ exists: { field: "targetDiastole" } });
        query.query.bool.must.push({ exists: { field: "targetSystole" } });
      }
    }

    const response = await client.search({
      index: indexName,
      body: query
    });
    return response.body.hits.hits.map(hit => ({ id: hit._id, ...hit._source }));
  }));
  // Flatten the array of arrays into a single array
  const data = results.flat();
  return data;
}

async function getLatestTargetsAchieved(userId, targetIds) {
  const client = getClient(); 

  // Construct the aggregation query to get the latest document for each targetId
  const response = await client.search({
    index: indexName,
    body: {
      size: 0,
      query: {
        bool: {
          must: [
            { match: { "userId.keyword": userId } },
            { terms: { targetId: targetIds } }
          ]
        }
      },
      aggs: {
        latest_targets: {
          terms: {
            field: "targetId",
            size: targetIds.length
          },
          aggs: {
            latest_doc: { 
              top_hits: { 
                size: 1,
                sort: [{ date: "desc" }] 
              }
            }
          }
        }
      }
    }
  });

  // Extract the latest documents for each targetId
  const data = response.body.aggregations.latest_targets.buckets.map(bucket => ({
    id: bucket.latest_doc.hits.hits[0]._id,
    ...bucket.latest_doc.hits.hits[0]._source
  }));

  return data;
}

async function getTargetsAchievedByIsMet(userId, startDate, endDate, targetIds, isMet = true, fetchNullTarget = false) {
  const client = getClient();
  let query = {
    sort: [{ date: { order: "desc" } }],
    size: 300,
    query: {
      bool: {
        must: [
          { match: { "userId.keyword": userId } },
          { match: { isMet }},
          { range: { date: { from: startDate, to: endDate } } },
        ],
      },
    },
  };

  targetIds = targetIds?.filter(x => x !== '');
  query.query.bool.filter = {
    terms: { targetId: targetIds }
  };

  if (!fetchNullTarget) {
    query.query.bool.must.push({ exists: { field: "target" } });
  }
  // BP target data, need to call explicitly, as keys are different
  let BPData = [];
  if(targetIds.includes(19) || targetIds.includes('19')) {
    BPData = await getTargetAchievedByIdAndDateRange(userId, 19, '2024-01-01', endDate, fetchNullTarget);
    BPData = BPData.filter(doc => doc?.isMet == isMet);
  }
  const response = await client.search({
    index: indexName,
    body: query,
  });
  
  if (!response.body?.hits) {
    return BPData;
  } else if (response.body?.hits) {
    const data = response.body?.hits?.hits?.map((itm) => {
      return { id: itm._id, ...itm._source };
    });
    return BPData.length > 0 ? [...BPData, ...data] : data;
  }
  return [];
}

function getAllTargets(givenTrackerId, targetsMap) {
  givenTrackerId = parseInt(givenTrackerId)
  return Object.keys(targetsMap).filter(targetId =>
    targetsMap[targetId].trackerIds.includes(givenTrackerId)
  ) || []
}

module.exports = {
  getAllTargets,
  getTargetsAchievedByDateRange,
  getTargetAchievedByIdAndDateRange,
  getTargetsAchievedByPagination,
  getTargetAchievedByIdAndPagination,
  getLatestTargetsAchieved,
  getTargetsAchievedByIsMet,
}
