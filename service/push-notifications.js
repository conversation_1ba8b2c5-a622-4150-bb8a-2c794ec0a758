const { sendPushNotification, getConnectedUserList } = require("../utils/aws");
const { log } = require("../utils/logger");

const notificationEventNames = {
  newDeviceConnected: "newDeviceConnected",
  newTargetAssigned: "newTargetAssigned",
};

const notificationDescription = {
  newDeviceConnected: {
    type: "trackers",
    title: "20 Degrees",
    alert: "A user connected with a new device",
  },
  newTargetAssigned: {
    type: "trackers",
    title: "20 Degrees",
    alert: "A new target is assigned",
  },
};

async function sendNotificationToUser(senderUserId, userId, notificationName, customData) {
  try {
    const { alert, title, type } = notificationDescription[notificationName];
    if (!alert || !title || !type) {
      return false;
    }
    const flag = await sendPushNotification(senderUserId, userId, alert, title, type, customData);
    return flag;
  } catch (error) {
    log.warn(`Error calling sendNotificationToUser, ${JSON.stringify(error)}`);
    return false;
  }
}

// if userId param belongs to BUSER, then it will send a notification to each connected USERS
// vice versa, if userId param belongs to USER, then it will send a notification to each connected BUSERS
async function sendNotificationToConnectedUsers(userId, notificationName, customData) {
  try {
    const { alert, title, type } = notificationDescription[notificationName];
    const connectedUsers = await getConnectedUserList(userId);
    if (!alert || !title || !type || !connectedUsers) {
      return false;
    }
    await Promise.all(connectedUsers.map((buserId) =>
        sendPushNotification(userId, buserId, alert, title, type, customData)
    ));
    return true;
  } catch (error) {
    log.warn(`Error calling sendNotificationToConnectedBUsers, ${JSON.stringify(error)}`);
    return false;
  }
}

module.exports = {
  notificationEventNames,
  notificationDescription,
  sendNotificationToUser,
  sendNotificationToConnectedUsers,
};
