const { getClient } = require("../utils/connection");
const { config } = require("../environment/index");
const { log } = require("../utils/logger");
const { getStaticTrackers, getStaticTargetsMap }  = require("../utils/staticData")
const utils = require("./utils.js");
const indexName = config.INDEX.trackers;

async function insertData(document) {
  return await utils.insertData(indexName, document)
}

async function getAllDefaultTrackers(userId) {
  const client = await getClient();

  const response = await client.search({
    index: indexName,
    body: {
      query: { match: { "userId.keyword": userId } },
    },
  });

  const data = response.body?.hits?.hits[0]?._source || {};
  return data;
}

async function updateDefaultTrackers(userId, document) {
  const client = await getClient();
  try{
    const response = await client.search({
      index: indexName,
      body: {
        query: { match: { "userId.keyword": userId } },
      },
    });
    const id = response.body?.hits?.hits[0]?._id || null;

    if (id) {
      const data = response.body?.hits?.hits[0]?._source || {};
      const defaultDevices = data?.defaultDevices || [];
      const newDevices = document?.defaultDevices || [];

      const tempMap = {};
      const res = [];
      newDevices.forEach(el => {
        if(!tempMap[el.trackerId]) {
          res.push(el); tempMap[el.trackerId] = 1;
        };
      });
      defaultDevices.forEach(el => {
        if(!tempMap[el.trackerId]) {
          res.push(el); tempMap[el.trackerId] = 1;
        };
      });
      document.defaultDevices = res;

      await client.update({
        index: indexName, id,
        body: { doc: { ...document } }
      });
      return id;
    } else {
      return await insertData(document);
    }
  } catch (error) {
    log.warn("Failed to upsert Trackers", JSON.stringify(error));
  }
}

async function upsertTrackers(userId, document) {
  const client = await getClient();
  try{
    const response = await client.search({
      index: indexName,
      body: {
        query: { match: { "userId.keyword": userId } },
      },
    });
    const id = response.body?.hits?.hits[0]?._id || null;

    if (id) {
      await client.update({
        index: indexName, id,
        body: { doc: { ...document } }
      });
      return id;
    } else {
      return await insertData(document);
    }
  } catch (error) {
    log.warn("Failed to upsert Trackers", JSON.stringify(error));
  }
}

async function getTrackerDetailsById(trackerId) {
  const trackerMap = await getStaticTrackers();
  const foundTracker = trackerMap.flatMap(tracker => tracker.subCategories).find(subCategory => subCategory.id === trackerId);
  return foundTracker || null;
}

async function getTrackerIdsByCategory(category) {
  const trackerMap = await getStaticTrackers();
  const result = trackerMap.find(item => item.category === category);
  return result?.subCategories?.map(sub => sub.id) || [];
}

async function getTrackersIdsByTargetIds(targetIds) {
  const targetMap = await getStaticTargetsMap();
  const trackerIds = [];
  targetIds.forEach(targetId => {
    const entry = targetMap[targetId];
    if (entry && entry.trackerIds) {
      trackerIds.push(...entry.trackerIds);
    }
  });

  return trackerIds.filter((id, index) => trackerIds.indexOf(id) === index);
}

const trackersService = {
  getAllDefaultTrackers,
  updateDefaultTrackers,
  upsertTrackers,
  getTrackerDetailsById,
  getTrackerIdsByCategory,
  getTrackersIdsByTargetIds,
};

module.exports = {
  trackersService,
};
